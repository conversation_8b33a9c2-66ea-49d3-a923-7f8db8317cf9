{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:17.297",
                    "level": "INFO",
                    "thread": "main",
                    "class": "com.genco.admin.GencoAdminApplication",
                    "message": "Starting GencoAdminApplication on LAPTOP-3I7P6FTL with PID 19104 (C:\Users\<USER>\Desktop\shop\api\genco-admin\target\classes started by 吴兴龙 in C:\Users\<USER>\Desktop\shop)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:17.300",
                    "level": "INFO",
                    "thread": "main",
                    "class": "com.genco.admin.GencoAdminApplication",
                    "message": "The following profiles are active: prod" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:19.176",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.s.d.r.c.RepositoryConfigurationDelegate",
                    "message": "Multiple Spring Data modules found, entering strict repository configuration mode!" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:19.180",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.s.d.r.c.RepositoryConfigurationDelegate",
                    "message": "Bootstrapping Spring Data Redis repositories in DEFAULT mode." }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:19.240",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.s.d.r.c.RepositoryConfigurationDelegate",
                    "message": "Finished Spring Data repository scanning in 37ms. Found 0 Redis repository interfaces." }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:20.171",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker",
                    "message": "Bean 'org.springframework.security.config.annotation.configuration.ObjectPostProcessorConfiguration' of type [org.springframework.security.config.annotation.configuration.ObjectPostProcessorConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:20.178",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker",
                    "message": "Bean 'objectPostProcessor' of type [org.springframework.security.config.annotation.configuration.AutowireBeanFactoryObjectPostProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:20.183",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker",
                    "message": "Bean 'org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler@4a9b3956' of type [org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:20.185",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker",
                    "message": "Bean 'org.springframework.security.config.annotation.method.configuration.GlobalMethodSecurityConfiguration' of type [org.springframework.security.config.annotation.method.configuration.GlobalMethodSecurityConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:20.194",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker",
                    "message": "Bean 'methodSecurityMetadataSource' of type [org.springframework.security.access.method.DelegatingMethodSecurityMetadataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:20.580",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.s.b.w.embedded.tomcat.TomcatWebServer",
                    "message": "Tomcat initialized with port(s): 20000 (http)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:20.592",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.apache.coyote.http11.Http11NioProtocol",
                    "message": "Initializing ProtocolHandler ["http-nio-20000"]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:20.593",
                    "level": "INFO",
                    "thread": "main",
                    "class": "org.apache.catalina.core.StandardService",
                    "message": "Starting service [Tomcat]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:20.593",
                    "level": "INFO",
                    "thread": "main",
                    "class": "org.apache.catalina.core.StandardEngine",
                    "message": "Starting Servlet engine: [Apache Tomcat/9.0.33]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:20.868",
                    "level": "INFO",
                    "thread": "main",
                    "class": "org.apache.jasper.servlet.TldScanner",
                    "message": "At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time." }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:20.878",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.a.c.c.C.[Tomcat].[localhost].[/]",
                    "message": "Initializing Spring embedded WebApplicationContext" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:20.878",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.s.web.context.ContextLoader",
                    "message": "Root WebApplicationContext: initialization completed in 3484 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:27.710",
                    "level": "INFO",
                    "thread": "main",
                    "class": "c.g.s.s.impl.PaymentStrategyFactory",
                    "message": "支付策略初始化完成，支持的支付类型: [offline, alipay, weixin, xendit, yue, haipay]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:29.717",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.s.b.a.e.web.EndpointLinksResolver",
                    "message": "Exposing 2 endpoint(s) beneath base path '/actuator'" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:29.909",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.PropertySourcedRequestMappingHandlerMapping",
                    "message": "Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:29.991",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.s.s.concurrent.ThreadPoolTaskExecutor",
                    "message": "Initializing ExecutorService" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:29.993",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.s.s.concurrent.ThreadPoolTaskExecutor",
                    "message": "Initializing ExecutorService 'taskExecutor'" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:30.313",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.s.s.web.DefaultSecurityFilterChain",
                    "message": "Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@363e2009, org.springframework.security.web.context.SecurityContextPersistenceFilter@13f17b8f, org.springframework.security.web.header.HeaderWriterFilter@1c76b2fe, org.springframework.web.filter.CorsFilter@6528d339, org.springframework.web.filter.CorsFilter@6528d339, org.springframework.web.filter.CorsFilter@6528d339, org.springframework.security.web.authentication.logout.LogoutFilter@3bdc8975, com.genco.admin.filter.JwtAuthenticationTokenFilter@4b99648a, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@11a29c0, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@212be8c, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@7b55fc83, org.springframework.security.web.session.SessionManagementFilter@6e5a77ef, org.springframework.security.web.access.ExceptionTranslationFilter@5abc488d, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@141c66db]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:32.265",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.p.DocumentationPluginsBootstrapper",
                    "message": "Context refreshed" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:32.355",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.p.DocumentationPluginsBootstrapper",
                    "message": "Found 2 custom documentation plugin(s)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:32.587",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.s.ApiListingReferenceScanner",
                    "message": "Scanning for api listing references" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:33.293",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_1" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:33.306",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getByIdsUsingGET_1" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:33.316",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_1" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:33.330",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_1" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:33.346",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_1" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:33.347",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_1" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:33.389",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_2" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:33.392",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_2" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:33.431",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_2" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:33.437",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_3" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:33.462",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_3" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:33.508",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updatePhoneUsingPOST_1" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:33.525",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_4" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:33.594",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_2" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:33.605",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_5" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:33.607",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_3" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:33.616",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_2" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:33.618",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_4" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:33.622",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_6" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:33.625",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_7" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:33.632",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_3" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:33.681",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_8" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:33.717",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_4" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:33.741",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_3" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:33.742",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_5" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:33.786",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_4" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:33.821",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_9" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:33.830",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_5" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:33.843",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_4" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:33.845",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_6" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:33.847",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateStatusUsingPOST_1" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:33.883",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_10" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:33.895",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_5" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:33.896",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateStatusUsingPOST_2" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:33.909",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_11" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:33.918",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_5" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:33.961",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_12" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.030",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_6" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.069",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_13" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.092",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_6" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.154",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_14" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.175",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_7" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.189",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_6" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.191",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_7" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.196",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_7" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.212",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_15" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.215",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_8" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.219",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_7" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.221",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_8" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.228",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_16" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.229",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_9" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.231",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_8" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.232",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_8" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.238",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_17" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.239",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_9" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.264",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_18" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.266",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_10" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.274",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_9" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.275",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_9" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.277",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateStatusUsingPOST_3" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.278",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_10" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.286",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_19" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.289",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_11" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.293",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_10" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.294",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_10" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.295",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateStatusUsingPOST_4" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.295",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_11" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.303",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_20" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.309",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_12" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.315",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_11" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.320",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_11" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.325",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_12" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.330",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_21" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.333",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_13" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.336",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_12" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.337",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_12" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.347",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_22" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.354",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListTreeUsingGET_1" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.357",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_14" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.364",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_13" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.365",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateStatusUsingPOST_5" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.379",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_15" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.395",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_23" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.397",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_16" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.400",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_13" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.401",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_14" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.403",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_13" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.410",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_24" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.413",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_17" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.420",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_14" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.423",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_15" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.424",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_14" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.434",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_25" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.436",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_18" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.440",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_15" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.442",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_16" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.449",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingPOST_1" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.460",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_26" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.461",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_19" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.461",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_17" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.470",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_27" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.483",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_20" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.491",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_18" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.494",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_15" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.506",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_28" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.512",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_21" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.515",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_16" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.516",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_19" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.517",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateStatusUsingGET_1" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.518",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_16" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.525",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_29" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.526",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_22" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.534",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_17" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.535",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_20" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.536",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateStatusUsingGET_2" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.540",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_17" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.550",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_30" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.553",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_23" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.559",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_18" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.559",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_21" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.560",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateStatusUsingGET_3" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.561",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingPOST_2" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.563",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_31" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.571",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_19" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.572",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_22" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.679",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_32" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.682",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_24" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.709",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_23" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.734",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_33" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.742",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_24" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.744",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateStatusUsingPOST_6" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.746",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_18" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.751",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_34" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.753",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_25" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.758",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_20" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.760",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_25" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.773",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingPOST_1" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.776",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_35" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.777",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: balanceUsingPOST_1" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.794",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_36" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.797",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_19" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.800",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_37" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.802",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_26" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.804",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_21" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.805",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_26" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.852",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.s.ApiListingReferenceScanner",
                    "message": "Scanning for api listing references" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.881",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.s.s.concurrent.ThreadPoolTaskScheduler",
                    "message": "Initializing ExecutorService" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.902",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-1",
                    "class": "c.g.a.task.tiktok.TiktokTokenRefreshTask",
                    "message": "---TiktokTokenRefreshTask task------produce Data with fixed rate task: Execution Time - Thu Aug 07 09:19:34 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:34.908",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.apache.coyote.http11.Http11NioProtocol",
                    "message": "Starting ProtocolHandler ["http-nio-20000"]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:35.343",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.s.b.w.embedded.tomcat.TomcatWebServer",
                    "message": "Tomcat started on port(s): 20000 (http) with context path ''" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:35.357",
                    "level": "INFO",
                    "thread": "main",
                    "class": "com.genco.admin.GencoAdminApplication",
                    "message": "Started GencoAdminApplication in 18.903 seconds (JVM running for 20.011)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:35.666",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-2",
                    "class": "com.alibaba.druid.pool.DruidDataSource",
                    "message": "{dataSource-1} inited" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:36.512",
                    "level": "INFO",
                    "thread": "RMI TCP Connection(8)-************",
                    "class": "o.a.c.c.C.[Tomcat].[localhost].[/]",
                    "message": "Initializing Spring DispatcherServlet 'dispatcherServlet'" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:36.513",
                    "level": "INFO",
                    "thread": "RMI TCP Connection(8)-************",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Initializing Servlet 'dispatcherServlet'" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:36.546",
                    "level": "INFO",
                    "thread": "RMI TCP Connection(8)-************",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed initialization in 33 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:38.767",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-1",
                    "class": "c.g.a.task.tiktok.TiktokTokenRefreshTask",
                    "message": "Tiktok access_token 未到刷新时间，当前时间: 1754529574，过期时间: 1781838424" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:39.977",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-2",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "开始同步TikTok订单 - pageToken: null, startTime: Mon Aug 04 09:19:19 CST 2025, endTime: Thu Aug 07 09:19:19 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:44.768",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-2",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "开始调用TikTok API获取订单数据" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:47.172",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-2",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "API调用成功，获取到下一页token: , 开始处理订单数据" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:47.425",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-2",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "开始处理 3 个订单" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:48.217",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-2",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "订单批量处理完成 - 总计: 3, 新增: 0, 更新: 0, 跳过: 3, 错误: 0" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:48.677",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-2",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "TikTok订单同步事务执行完成，下一页token: " }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:48.677",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-2",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "TikTok订单同步方法执行完成，返回下一页token: " }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:20:50.338",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-1",
                    "class": "c.g.admin.task.order.OrderTiktokPullTask",
                    "message": "创建TikTok订单拉取初始任务，时间范围：Mon Aug 04 09:20:49 CST 2025 至 Thu Aug 07 09:20:49 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:21:51.513",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-7",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "开始同步TikTok订单 - pageToken: null, startTime: Mon Aug 04 09:21:20 CST 2025, endTime: Thu Aug 07 09:21:20 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:21:52.199",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-7",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "开始调用TikTok API获取订单数据" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:21:52.570",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-7",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "API调用成功，获取到下一页token: , 开始处理订单数据" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:21:52.796",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-7",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "开始处理 3 个订单" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:21:53.555",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-7",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "订单批量处理完成 - 总计: 3, 新增: 0, 更新: 0, 跳过: 3, 错误: 0" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:21:54.059",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-7",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "TikTok订单同步事务执行完成，下一页token: " }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:21:54.059",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-7",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "TikTok订单同步方法执行完成，返回下一页token: " }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:22:55.693",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-3",
                    "class": "c.g.admin.task.order.OrderTiktokPullTask",
                    "message": "创建TikTok订单拉取初始任务，时间范围：Mon Aug 04 09:22:55 CST 2025 至 Thu Aug 07 09:22:55 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:22:58.006",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-6",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: 1399955699384b32bd5617b0190980b4, 类名: ValidateCodeController, 方法名: get, 用户ID: null, URI: /api/admin/validate/code/get, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:22:58.006",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-19",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: 1aca5917c88b49868cd11cc42be5c076, 类名: AdminLoginController, 方法名: getLoginPic, 用户ID: null, URI: /api/admin/getLoginPic, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:22:58.189",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-6",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"1399955699384b32bd5617b0190980b4","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"get","requestParams":{"request":{"temp":"**********"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/validate/code/get","url":"http://127.0.0.1:20000/api/admin/validate/code/get"},"className":"com.genco.admin.controller.ValidateCodeController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","uri":"/api/admin/validate/code/get"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:22:58.189",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-19",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"1aca5917c88b49868cd11cc42be5c076","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"getLoginPic","requestParams":{"request":{"temp":"**********"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/getLoginPic","url":"http://127.0.0.1:20000/api/admin/getLoginPic"},"className":"com.genco.admin.controller.AdminLoginController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","uri":"/api/admin/getLoginPic"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:22:58.990",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-6",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.ValidateCodeController.get()，prams：[]，cost time：660542700 ns，cost：660 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:22:59.065",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-6",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: 1399955699384b32bd5617b0190980b4, 类名: ValidateCodeController, 方法名: get, 用户ID: null, 是否成功: true, 响应时间: 1059ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:22:59.065",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-6",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"1399955699384b32bd5617b0190980b4","requestBody":"未记录","responseTime":1059,"methodName":"get","className":"com.genco.admin.controller.ValidateCodeController","isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:00.008",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-19",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.AdminLoginController.getLoginPic()，prams：[]，cost time：********** ns，cost：1689 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:00.013",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-19",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: 1aca5917c88b49868cd11cc42be5c076, 类名: AdminLoginController, 方法名: getLoginPic, 用户ID: null, 是否成功: true, 响应时间: 2007ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:00.013",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-19",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"1aca5917c88b49868cd11cc42be5c076","requestBody":"未记录","responseTime":2007,"methodName":"getLoginPic","className":"com.genco.admin.controller.AdminLoginController","isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:05.611",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-14",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: 51c055de0f564957905915b6b3fd7295, 类名: AdminLoginController, 方法名: SystemAdminLogin, 用户ID: null, URI: /api/admin/login, 方法: POST" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:05.611",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-14",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"51c055de0f564957905915b6b3fd7295","method":"POST","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"SystemAdminLogin","requestParams":{"request":{},"method":"POST","request_filter":{},"header":{"sec-fetch-mode":"cors","content-length":"107","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","content-type":"application/json","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/login","url":"http://127.0.0.1:20000/api/admin/login"},"className":"com.genco.admin.controller.AdminLoginController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","uri":"/api/admin/login"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.228",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-14",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.AdminLoginController.SystemAdminLogin(SystemAdminLoginRequest,HttpServletRequest)，prams：[SystemAdminLoginRequest(account=admin, pwd=BzX36YjvNR8o, key=3e0c35242e766e78e3660855a2ad46c2, code=djuh), SecurityContextHolderAwareRequestWrapper[ org.springframework.security.web.header.HeaderWriterFilter$HeaderWriterRequest@24f585e]]，cost time：********** ns，cost：2091 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.228",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-14",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: 51c055de0f564957905915b6b3fd7295, 类名: AdminLoginController, 方法名: SystemAdminLogin, 用户ID: null, 是否成功: true, 响应时间: 2617ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.228",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-14",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"51c055de0f564957905915b6b3fd7295","requestBody":"未记录","responseTime":2617,"methodName":"SystemAdminLogin","className":"com.genco.admin.controller.AdminLoginController","isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.311",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-30",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: 2ebc18ed0fa14a9cac92e31e2081a329, 类名: SystemStoreStaffController, 方法名: getList, 用户ID: 1, URI: /api/admin/system/store/staff/list, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.311",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-30",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"2ebc18ed0fa14a9cac92e31e2081a329","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"getList","requestParams":{"request":{"temp":"1754529788","limit":"9999","page":"1"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"f314572531e74f07ae6e19bc2c87d804","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/system/store/staff/list","url":"http://127.0.0.1:20000/api/admin/system/store/staff/list"},"className":"com.genco.admin.controller.SystemStoreStaffController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/system/store/staff/list"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.311",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-7",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: 787346fae7094e628ecbbc5916b68241, 类名: AdminLoginController, 方法名: getAdminInfo, 用户ID: 1, URI: /api/admin/getAdminInfoByToken, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.311",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-7",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"787346fae7094e628ecbbc5916b68241","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"getAdminInfo","requestParams":{"request":{"temp":"1754529788","token":"f314572531e74f07ae6e19bc2c87d804"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"f314572531e74f07ae6e19bc2c87d804","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/getAdminInfoByToken","url":"http://127.0.0.1:20000/api/admin/getAdminInfoByToken"},"className":"com.genco.admin.controller.AdminLoginController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/getAdminInfoByToken"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.336",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-7",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.AdminLoginController.getAdminInfo()，prams：[]，cost time：10666700 ns，cost：10 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.344",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-7",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: 787346fae7094e628ecbbc5916b68241, 类名: AdminLoginController, 方法名: getAdminInfo, 用户ID: 1, 是否成功: true, 响应时间: 33ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.344",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-7",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"787346fae7094e628ecbbc5916b68241","requestBody":"未记录","responseTime":33,"methodName":"getAdminInfo","className":"com.genco.admin.controller.AdminLoginController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.361",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-5",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: 33dc38360a8a4af8809d98b998357e36, 类名: AdminLoginController, 方法名: getMenus, 用户ID: 1, URI: /api/admin/getMenus, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.361",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-5",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"33dc38360a8a4af8809d98b998357e36","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"getMenus","requestParams":{"request":{"temp":"1754529788"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"f314572531e74f07ae6e19bc2c87d804","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/getMenus","url":"http://127.0.0.1:20000/api/admin/getMenus"},"className":"com.genco.admin.controller.AdminLoginController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/getMenus"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.593",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-30",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemStoreStaffController.getList(Integer,PageParamRequest)，prams：[0, PageParamRequest(page=1, limit=9999)]，cost time：247310600 ns，cost：247 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.597",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-30",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: 2ebc18ed0fa14a9cac92e31e2081a329, 类名: SystemStoreStaffController, 方法名: getList, 用户ID: 1, 是否成功: true, 响应时间: 284ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.597",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-30",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"2ebc18ed0fa14a9cac92e31e2081a329","requestBody":"未记录","responseTime":284,"methodName":"getList","className":"com.genco.admin.controller.SystemStoreStaffController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.620",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-5",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.AdminLoginController.getMenus()，prams：[]，cost time：260134400 ns，cost：260 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.631",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-5",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: 33dc38360a8a4af8809d98b998357e36, 类名: AdminLoginController, 方法名: getMenus, 用户ID: 1, 是否成功: true, 响应时间: 268ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.631",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-5",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"33dc38360a8a4af8809d98b998357e36","requestBody":"未记录","responseTime":268,"methodName":"getMenus","className":"com.genco.admin.controller.AdminLoginController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.705",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-1",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: a36202a3c7a54e8399638f7c5c4b0863, 类名: SystemConfigController, 方法名: info, 用户ID: 1, URI: /api/admin/system/config/info, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.705",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-9",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: 28a28a7795c6439fad2a550e0d4c28fa, 类名: SystemConfigController, 方法名: justGetUniq, 用户ID: 1, URI: /api/admin/system/config/getuniq, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.705",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-1",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"a36202a3c7a54e8399638f7c5c4b0863","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"info","requestParams":{"request":{"formId":"100","temp":"1754529788"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"f314572531e74f07ae6e19bc2c87d804","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/system/config/info","url":"http://127.0.0.1:20000/api/admin/system/config/info"},"className":"com.genco.admin.controller.SystemConfigController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/system/config/info"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.705",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-9",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"28a28a7795c6439fad2a550e0d4c28fa","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"justGetUniq","requestParams":{"request":{"temp":"1754529788","key":"site_logo_lefttop"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"f314572531e74f07ae6e19bc2c87d804","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/system/config/getuniq","url":"http://127.0.0.1:20000/api/admin/system/config/getuniq"},"className":"com.genco.admin.controller.SystemConfigController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/system/config/getuniq"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.705",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-18",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: a0bf5cd3896b4fd3af9b8dae0af741bd, 类名: SystemConfigController, 方法名: justGetUniq, 用户ID: 1, URI: /api/admin/system/config/getuniq, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.705",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-18",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"a0bf5cd3896b4fd3af9b8dae0af741bd","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"justGetUniq","requestParams":{"request":{"temp":"1754529788","key":"site_logo_square"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"f314572531e74f07ae6e19bc2c87d804","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/system/config/getuniq","url":"http://127.0.0.1:20000/api/admin/system/config/getuniq"},"className":"com.genco.admin.controller.SystemConfigController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/system/config/getuniq"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.812",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-9",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemConfigController.justGetUniq(String)，prams：[site_logo_lefttop]，cost time：106150700 ns，cost：106 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.821",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-9",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: 28a28a7795c6439fad2a550e0d4c28fa, 类名: SystemConfigController, 方法名: justGetUniq, 用户ID: 1, 是否成功: true, 响应时间: 109ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.821",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-9",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"28a28a7795c6439fad2a550e0d4c28fa","requestBody":"未记录","responseTime":109,"methodName":"justGetUniq","className":"com.genco.admin.controller.SystemConfigController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.903",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-1",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemConfigController.info(Integer)，prams：[100]，cost time：200484600 ns，cost：200 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.911",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-1",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: a36202a3c7a54e8399638f7c5c4b0863, 类名: SystemConfigController, 方法名: info, 用户ID: 1, 是否成功: true, 响应时间: 200ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.911",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-1",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"a36202a3c7a54e8399638f7c5c4b0863","requestBody":"未记录","responseTime":200,"methodName":"info","className":"com.genco.admin.controller.SystemConfigController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.944",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-18",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemConfigController.justGetUniq(String)，prams：[site_logo_square]，cost time：229882000 ns，cost：229 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.944",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-18",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: a0bf5cd3896b4fd3af9b8dae0af741bd, 类名: SystemConfigController, 方法名: justGetUniq, 用户ID: 1, 是否成功: true, 响应时间: 241ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.944",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-18",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"a0bf5cd3896b4fd3af9b8dae0af741bd","requestBody":"未记录","responseTime":241,"methodName":"justGetUniq","className":"com.genco.admin.controller.SystemConfigController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:12.718",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-11",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: 3bfeff0e7b154b9a99658b7e7b22d7a5, 类名: StoreBrandController, 方法名: getBrandList, 用户ID: 1, URI: /api/admin/brand/list, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:12.719",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-28",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: a75a6c809a8b46d088ae9e08ed7e6523, 类名: SystemGroupDataController, 方法名: getList, 用户ID: 1, URI: /api/admin/system/group/data/list, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:12.719",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-11",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"3bfeff0e7b154b9a99658b7e7b22d7a5","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"getBrandList","requestParams":{"request":{"temp":"1754529792","limit":"20","name":"","page":"1","type":"-1"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"f314572531e74f07ae6e19bc2c87d804","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/brand/list","url":"http://127.0.0.1:20000/api/admin/brand/list"},"className":"com.genco.admin.controller.StoreBrandController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/brand/list"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:12.719",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-28",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"a75a6c809a8b46d088ae9e08ed7e6523","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"getList","requestParams":{"request":{"temp":"1754529792","gid":"74"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"f314572531e74f07ae6e19bc2c87d804","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/system/group/data/list","url":"http://127.0.0.1:20000/api/admin/system/group/data/list"},"className":"com.genco.admin.controller.SystemGroupDataController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/system/group/data/list"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:13.153",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-28",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemGroupDataController.getList(SystemGroupDataSearchRequest,PageParamRequest)，prams：[SystemGroupDataSearchRequest(keywords=null, gid=74, status=null), PageParamRequest(page=1, limit=20)]，cost time：422685599 ns，cost：422 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:13.162",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-28",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: a75a6c809a8b46d088ae9e08ed7e6523, 类名: SystemGroupDataController, 方法名: getList, 用户ID: 1, 是否成功: true, 响应时间: 450ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:13.163",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-28",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"a75a6c809a8b46d088ae9e08ed7e6523","requestBody":"未记录","responseTime":450,"methodName":"getList","className":"com.genco.admin.controller.SystemGroupDataController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:17.853",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-11",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.StoreBrandController.getBrandList(Integer,String,PageParamRequest)，prams：[-1, , PageParamRequest(page=1, limit=20)]，cost time：5127553400 ns，cost：5127 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:17.869",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-11",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: 3bfeff0e7b154b9a99658b7e7b22d7a5, 类名: StoreBrandController, 方法名: getBrandList, 用户ID: 1, 是否成功: true, 响应时间: 5157ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:17.869",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-11",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"3bfeff0e7b154b9a99658b7e7b22d7a5","requestBody":"未记录","responseTime":5157,"methodName":"getBrandList","className":"com.genco.admin.controller.StoreBrandController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:56.857",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-10",
                    "class": "c.g.admin.task.order.OrderTiktokPullTask",
                    "message": "创建TikTok订单拉取初始任务，时间范围：Mon Aug 04 09:23:56 CST 2025 至 Thu Aug 07 09:23:56 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:24:58.025",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-6",
                    "class": "c.g.admin.task.order.OrderTiktokPullTask",
                    "message": "创建TikTok订单拉取初始任务，时间范围：Mon Aug 04 09:24:57 CST 2025 至 Thu Aug 07 09:24:57 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:25:59.174",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-13",
                    "class": "c.g.admin.task.order.OrderTiktokPullTask",
                    "message": "创建TikTok订单拉取初始任务，时间范围：Mon Aug 04 09:25:58 CST 2025 至 Thu Aug 07 09:25:58 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:27:00.366",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-2",
                    "class": "c.g.admin.task.order.OrderTiktokPullTask",
                    "message": "创建TikTok订单拉取初始任务，时间范围：Mon Aug 04 09:26:59 CST 2025 至 Thu Aug 07 09:26:59 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:28:01.543",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-16",
                    "class": "c.g.admin.task.order.OrderTiktokPullTask",
                    "message": "创建TikTok订单拉取初始任务，时间范围：Mon Aug 04 09:28:01 CST 2025 至 Thu Aug 07 09:28:01 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:29:02.706",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-9",
                    "class": "c.g.admin.task.order.OrderTiktokPullTask",
                    "message": "创建TikTok订单拉取初始任务，时间范围：Mon Aug 04 09:29:02 CST 2025 至 Thu Aug 07 09:29:02 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:29:38.785",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-5",
                    "class": "c.g.a.task.tiktok.TiktokTokenRefreshTask",
                    "message": "---TiktokTokenRefreshTask task------produce Data with fixed rate task: Execution Time - Thu Aug 07 09:29:38 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:29:39.020",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-5",
                    "class": "c.g.a.task.tiktok.TiktokTokenRefreshTask",
                    "message": "Tiktok access_token 未到刷新时间，当前时间: 1754530178，过期时间: 1781838424" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:30:03.886",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-10",
                    "class": "c.g.admin.task.order.OrderTiktokPullTask",
                    "message": "创建TikTok订单拉取初始任务，时间范围：Mon Aug 04 09:30:03 CST 2025 至 Thu Aug 07 09:30:03 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:31:05.059",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-21",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "开始同步TikTok订单 - pageToken: null, startTime: Mon Aug 04 09:30:50 CST 2025, endTime: Thu Aug 07 09:30:50 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:31:05.765",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-21",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "开始调用TikTok API获取订单数据" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:31:06.145",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-21",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "API调用成功，获取到下一页token: , 开始处理订单数据" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:31:06.376",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-21",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "开始处理 3 个订单" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:31:07.083",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-21",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "订单批量处理完成 - 总计: 3, 新增: 0, 更新: 0, 跳过: 3, 错误: 0" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:31:07.534",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-21",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "TikTok订单同步事务执行完成，下一页token: " }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:31:07.535",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-21",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "TikTok订单同步方法执行完成，返回下一页token: " }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:09.198",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-6",
                    "class": "c.g.admin.task.order.OrderTiktokPullTask",
                    "message": "创建TikTok订单拉取初始任务，时间范围：Mon Aug 04 09:32:08 CST 2025 至 Thu Aug 07 09:32:08 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:11.680",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-20",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: 95d585755471421884a3a46098b7cd5e, 类名: AdminLoginController, 方法名: getAdminInfo, 用户ID: 1, URI: /api/admin/getAdminInfoByToken, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:11.681",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-20",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"95d585755471421884a3a46098b7cd5e","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"getAdminInfo","requestParams":{"request":{"temp":"1754530331","token":"f314572531e74f07ae6e19bc2c87d804"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"f314572531e74f07ae6e19bc2c87d804","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/getAdminInfoByToken","url":"http://127.0.0.1:20000/api/admin/getAdminInfoByToken"},"className":"com.genco.admin.controller.AdminLoginController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/getAdminInfoByToken"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:11.687",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-20",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.AdminLoginController.getAdminInfo()，prams：[]，cost time：812501 ns，cost：0 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:11.694",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-20",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: 95d585755471421884a3a46098b7cd5e, 类名: AdminLoginController, 方法名: getAdminInfo, 用户ID: 1, 是否成功: true, 响应时间: 15ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:11.695",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-20",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"95d585755471421884a3a46098b7cd5e","requestBody":"未记录","responseTime":15,"methodName":"getAdminInfo","className":"com.genco.admin.controller.AdminLoginController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:11.716",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-22",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: ccf283182daa4311b39907a6e25ce040, 类名: AdminLoginController, 方法名: getMenus, 用户ID: 1, URI: /api/admin/getMenus, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:11.717",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-22",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"ccf283182daa4311b39907a6e25ce040","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"getMenus","requestParams":{"request":{"temp":"1754530331"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"f314572531e74f07ae6e19bc2c87d804","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/getMenus","url":"http://127.0.0.1:20000/api/admin/getMenus"},"className":"com.genco.admin.controller.AdminLoginController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/getMenus"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:11.953",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-22",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.AdminLoginController.getMenus()，prams：[]，cost time：234573900 ns，cost：234 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:11.955",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-22",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: ccf283182daa4311b39907a6e25ce040, 类名: AdminLoginController, 方法名: getMenus, 用户ID: 1, 是否成功: true, 响应时间: 240ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:11.956",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-22",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"ccf283182daa4311b39907a6e25ce040","requestBody":"未记录","responseTime":240,"methodName":"getMenus","className":"com.genco.admin.controller.AdminLoginController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:12.413",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-14",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: 209e7b2526e64402affafc294a7f5e80, 类名: SystemConfigController, 方法名: justGetUniq, 用户ID: 1, URI: /api/admin/system/config/getuniq, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:12.414",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-14",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"209e7b2526e64402affafc294a7f5e80","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"justGetUniq","requestParams":{"request":{"temp":"1754530332","key":"site_logo_lefttop"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"f314572531e74f07ae6e19bc2c87d804","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/system/config/getuniq","url":"http://127.0.0.1:20000/api/admin/system/config/getuniq"},"className":"com.genco.admin.controller.SystemConfigController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/system/config/getuniq"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:12.414",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-12",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: b7bcc9de61d64f7a95b16f32a72a6325, 类名: SystemConfigController, 方法名: justGetUniq, 用户ID: 1, URI: /api/admin/system/config/getuniq, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:12.415",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-12",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"b7bcc9de61d64f7a95b16f32a72a6325","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"justGetUniq","requestParams":{"request":{"temp":"1754530332","key":"site_logo_square"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"f314572531e74f07ae6e19bc2c87d804","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/system/config/getuniq","url":"http://127.0.0.1:20000/api/admin/system/config/getuniq"},"className":"com.genco.admin.controller.SystemConfigController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/system/config/getuniq"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:12.416",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-7",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: 7b22bb5ea0b14738877efd2d326a7183, 类名: SystemGroupDataController, 方法名: getList, 用户ID: 1, URI: /api/admin/system/group/data/list, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:12.418",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-7",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"7b22bb5ea0b14738877efd2d326a7183","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"getList","requestParams":{"request":{"temp":"1754530332","gid":"74"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"f314572531e74f07ae6e19bc2c87d804","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/system/group/data/list","url":"http://127.0.0.1:20000/api/admin/system/group/data/list"},"className":"com.genco.admin.controller.SystemGroupDataController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/system/group/data/list"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:12.418",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-13",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: 0a2808cf286c44d3b019aef4c8b7ea6f, 类名: StoreBrandController, 方法名: getBrandList, 用户ID: 1, URI: /api/admin/brand/list, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:12.418",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-13",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"0a2808cf286c44d3b019aef4c8b7ea6f","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"getBrandList","requestParams":{"request":{"temp":"1754530332","limit":"20","name":"","page":"1","type":"-1"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"f314572531e74f07ae6e19bc2c87d804","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/brand/list","url":"http://127.0.0.1:20000/api/admin/brand/list"},"className":"com.genco.admin.controller.StoreBrandController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/brand/list"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:12.644",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-14",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemConfigController.justGetUniq(String)，prams：[site_logo_lefttop]，cost time：227505401 ns，cost：227 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:12.648",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-14",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: 209e7b2526e64402affafc294a7f5e80, 类名: SystemConfigController, 方法名: justGetUniq, 用户ID: 1, 是否成功: true, 响应时间: 234ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:12.648",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-14",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"209e7b2526e64402affafc294a7f5e80","requestBody":"未记录","responseTime":234,"methodName":"justGetUniq","className":"com.genco.admin.controller.SystemConfigController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:12.767",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-7",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemGroupDataController.getList(SystemGroupDataSearchRequest,PageParamRequest)，prams：[SystemGroupDataSearchRequest(keywords=null, gid=74, status=null), PageParamRequest(page=1, limit=20)]，cost time：345122801 ns，cost：345 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:12.773",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-7",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: 7b22bb5ea0b14738877efd2d326a7183, 类名: SystemGroupDataController, 方法名: getList, 用户ID: 1, 是否成功: true, 响应时间: 356ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:12.773",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-7",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"7b22bb5ea0b14738877efd2d326a7183","requestBody":"未记录","responseTime":356,"methodName":"getList","className":"com.genco.admin.controller.SystemGroupDataController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:12.867",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-12",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemConfigController.justGetUniq(String)，prams：[site_logo_square]，cost time：448621300 ns，cost：448 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:12.870",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-12",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: b7bcc9de61d64f7a95b16f32a72a6325, 类名: SystemConfigController, 方法名: justGetUniq, 用户ID: 1, 是否成功: true, 响应时间: 457ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:12.870",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-12",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"b7bcc9de61d64f7a95b16f32a72a6325","requestBody":"未记录","responseTime":457,"methodName":"justGetUniq","className":"com.genco.admin.controller.SystemConfigController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:17.681",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-13",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.StoreBrandController.getBrandList(Integer,String,PageParamRequest)，prams：[-1, , PageParamRequest(page=1, limit=20)]，cost time：5258737100 ns，cost：5258 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:17.685",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-13",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: 0a2808cf286c44d3b019aef4c8b7ea6f, 类名: StoreBrandController, 方法名: getBrandList, 用户ID: 1, 是否成功: true, 响应时间: 5268ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:17.685",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-13",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"0a2808cf286c44d3b019aef4c8b7ea6f","requestBody":"未记录","responseTime":5268,"methodName":"getBrandList","className":"com.genco.admin.controller.StoreBrandController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:31.407",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-3",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: 9f6261b0cb8f4ed7a8bd99bb9729047f, 类名: AdminLoginController, 方法名: getAdminInfo, 用户ID: 1, URI: /api/admin/getAdminInfoByToken, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:31.407",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-3",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"9f6261b0cb8f4ed7a8bd99bb9729047f","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"getAdminInfo","requestParams":{"request":{"temp":"1754530351","token":"f314572531e74f07ae6e19bc2c87d804"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"f314572531e74f07ae6e19bc2c87d804","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/getAdminInfoByToken","url":"http://127.0.0.1:20000/api/admin/getAdminInfoByToken"},"className":"com.genco.admin.controller.AdminLoginController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/getAdminInfoByToken"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:31.408",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-3",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.AdminLoginController.getAdminInfo()，prams：[]，cost time：122200 ns，cost：0 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:31.412",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-3",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: 9f6261b0cb8f4ed7a8bd99bb9729047f, 类名: AdminLoginController, 方法名: getAdminInfo, 用户ID: 1, 是否成功: true, 响应时间: 6ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:31.413",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-3",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"9f6261b0cb8f4ed7a8bd99bb9729047f","requestBody":"未记录","responseTime":6,"methodName":"getAdminInfo","className":"com.genco.admin.controller.AdminLoginController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:31.438",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-2",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: ********************************, 类名: AdminLoginController, 方法名: getMenus, 用户ID: 1, URI: /api/admin/getMenus, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:31.438",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-2",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"********************************","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"getMenus","requestParams":{"request":{"temp":"1754530351"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"f314572531e74f07ae6e19bc2c87d804","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/getMenus","url":"http://127.0.0.1:20000/api/admin/getMenus"},"className":"com.genco.admin.controller.AdminLoginController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/getMenus"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:31.678",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-2",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.AdminLoginController.getMenus()，prams：[]，cost time：238851600 ns，cost：238 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:31.681",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-2",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: ********************************, 类名: AdminLoginController, 方法名: getMenus, 用户ID: 1, 是否成功: true, 响应时间: 244ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:31.681",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-2",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"********************************","requestBody":"未记录","responseTime":244,"methodName":"getMenus","className":"com.genco.admin.controller.AdminLoginController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:32.081",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-16",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: 39ef73c05f8c473f8ad6c83629f23029, 类名: SystemGroupDataController, 方法名: getList, 用户ID: 1, URI: /api/admin/system/group/data/list, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:32.081",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-16",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"39ef73c05f8c473f8ad6c83629f23029","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"getList","requestParams":{"request":{"temp":"1754530352","gid":"74"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"f314572531e74f07ae6e19bc2c87d804","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/system/group/data/list","url":"http://127.0.0.1:20000/api/admin/system/group/data/list"},"className":"com.genco.admin.controller.SystemGroupDataController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/system/group/data/list"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:32.083",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-28",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: b3e8c520a0e84e1bb2a07a5f92332a9e, 类名: SystemConfigController, 方法名: justGetUniq, 用户ID: 1, URI: /api/admin/system/config/getuniq, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:32.084",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-28",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"b3e8c520a0e84e1bb2a07a5f92332a9e","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"justGetUniq","requestParams":{"request":{"temp":"1754530352","key":"site_logo_square"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"f314572531e74f07ae6e19bc2c87d804","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/system/config/getuniq","url":"http://127.0.0.1:20000/api/admin/system/config/getuniq"},"className":"com.genco.admin.controller.SystemConfigController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/system/config/getuniq"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:32.084",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-11",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: 8480765928484828a43bf28c79128837, 类名: StoreBrandController, 方法名: getBrandList, 用户ID: 1, URI: /api/admin/brand/list, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:32.085",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-11",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"8480765928484828a43bf28c79128837","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"getBrandList","requestParams":{"request":{"temp":"1754530352","limit":"20","name":"","page":"1","type":"-1"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"f314572531e74f07ae6e19bc2c87d804","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/brand/list","url":"http://127.0.0.1:20000/api/admin/brand/list"},"className":"com.genco.admin.controller.StoreBrandController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/brand/list"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:32.088",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-27",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: 6d74a8f229524477b21fbd03295c4d69, 类名: SystemConfigController, 方法名: justGetUniq, 用户ID: 1, URI: /api/admin/system/config/getuniq, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:32.088",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-27",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"6d74a8f229524477b21fbd03295c4d69","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"justGetUniq","requestParams":{"request":{"temp":"1754530352","key":"site_logo_lefttop"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"f314572531e74f07ae6e19bc2c87d804","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/system/config/getuniq","url":"http://127.0.0.1:20000/api/admin/system/config/getuniq"},"className":"com.genco.admin.controller.SystemConfigController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/system/config/getuniq"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:32.243",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-16",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemGroupDataController.getList(SystemGroupDataSearchRequest,PageParamRequest)，prams：[SystemGroupDataSearchRequest(keywords=null, gid=74, status=null), PageParamRequest(page=1, limit=20)]，cost time：159857700 ns，cost：159 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:32.248",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-16",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: 39ef73c05f8c473f8ad6c83629f23029, 类名: SystemGroupDataController, 方法名: getList, 用户ID: 1, 是否成功: true, 响应时间: 165ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:32.248",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-16",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"39ef73c05f8c473f8ad6c83629f23029","requestBody":"未记录","responseTime":165,"methodName":"getList","className":"com.genco.admin.controller.SystemGroupDataController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:32.289",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-27",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemConfigController.justGetUniq(String)，prams：[site_logo_lefttop]，cost time：200291399 ns，cost：200 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:32.292",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-27",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: 6d74a8f229524477b21fbd03295c4d69, 类名: SystemConfigController, 方法名: justGetUniq, 用户ID: 1, 是否成功: true, 响应时间: 207ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:32.293",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-27",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"6d74a8f229524477b21fbd03295c4d69","requestBody":"未记录","responseTime":207,"methodName":"justGetUniq","className":"com.genco.admin.controller.SystemConfigController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:32.313",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-28",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemConfigController.justGetUniq(String)，prams：[site_logo_square]，cost time：228830701 ns，cost：228 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:32.316",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-28",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: b3e8c520a0e84e1bb2a07a5f92332a9e, 类名: SystemConfigController, 方法名: justGetUniq, 用户ID: 1, 是否成功: true, 响应时间: 235ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:32.317",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-28",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"b3e8c520a0e84e1bb2a07a5f92332a9e","requestBody":"未记录","responseTime":235,"methodName":"justGetUniq","className":"com.genco.admin.controller.SystemConfigController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:34.326",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-11",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.StoreBrandController.getBrandList(Integer,String,PageParamRequest)，prams：[-1, , PageParamRequest(page=1, limit=20)]，cost time：2240746300 ns，cost：2240 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:34.329",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-11",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: 8480765928484828a43bf28c79128837, 类名: StoreBrandController, 方法名: getBrandList, 用户ID: 1, 是否成功: true, 响应时间: 2247ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:34.329",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-11",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"8480765928484828a43bf28c79128837","requestBody":"未记录","responseTime":2247,"methodName":"getBrandList","className":"com.genco.admin.controller.StoreBrandController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:33:02.198",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-25",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: b79d1b0857a3444bb9868faf99f74e02, 类名: AdminLoginController, 方法名: getAdminInfo, 用户ID: 1, URI: /api/admin/getAdminInfoByToken, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:33:02.199",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-25",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"b79d1b0857a3444bb9868faf99f74e02","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"getAdminInfo","requestParams":{"request":{"temp":"1754530382","token":"f314572531e74f07ae6e19bc2c87d804"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"f314572531e74f07ae6e19bc2c87d804","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/getAdminInfoByToken","url":"http://127.0.0.1:20000/api/admin/getAdminInfoByToken"},"className":"com.genco.admin.controller.AdminLoginController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/getAdminInfoByToken"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:33:02.200",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-25",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.AdminLoginController.getAdminInfo()，prams：[]，cost time：180100 ns，cost：0 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:33:02.204",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-25",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: b79d1b0857a3444bb9868faf99f74e02, 类名: AdminLoginController, 方法名: getAdminInfo, 用户ID: 1, 是否成功: true, 响应时间: 5ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:33:02.204",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-25",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"b79d1b0857a3444bb9868faf99f74e02","requestBody":"未记录","responseTime":5,"methodName":"getAdminInfo","className":"com.genco.admin.controller.AdminLoginController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:33:02.220",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-23",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: 864ddf5294014c60ae3e74a85423712d, 类名: AdminLoginController, 方法名: getMenus, 用户ID: 1, URI: /api/admin/getMenus, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:33:02.220",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-23",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"864ddf5294014c60ae3e74a85423712d","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"getMenus","requestParams":{"request":{"temp":"1754530382"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"f314572531e74f07ae6e19bc2c87d804","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/getMenus","url":"http://127.0.0.1:20000/api/admin/getMenus"},"className":"com.genco.admin.controller.AdminLoginController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/getMenus"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:33:02.325",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-23",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.AdminLoginController.getMenus()，prams：[]，cost time：103364000 ns，cost：103 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:33:02.328",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-23",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: 864ddf5294014c60ae3e74a85423712d, 类名: AdminLoginController, 方法名: getMenus, 用户ID: 1, 是否成功: true, 响应时间: 110ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:33:02.328",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-23",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"864ddf5294014c60ae3e74a85423712d","requestBody":"未记录","responseTime":110,"methodName":"getMenus","className":"com.genco.admin.controller.AdminLoginController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:33:09.711",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-24",
                    "class": "c.g.admin.task.order.OrderTiktokPullTask",
                    "message": "创建TikTok订单拉取初始任务，时间范围：Mon Aug 04 09:33:09 CST 2025 至 Thu Aug 07 09:33:09 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:34:10.210",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-13",
                    "class": "c.g.admin.task.order.OrderTiktokPullTask",
                    "message": "创建TikTok订单拉取初始任务，时间范围：Mon Aug 04 09:34:10 CST 2025 至 Thu Aug 07 09:34:10 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:35:10.707",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-27",
                    "class": "c.g.admin.task.order.OrderTiktokPullTask",
                    "message": "创建TikTok订单拉取初始任务，时间范围：Mon Aug 04 09:35:10 CST 2025 至 Thu Aug 07 09:35:10 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:35:14.636",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-15",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: 66be7d7958194f58a057d6d3dd17922e, 类名: ValidateCodeController, 方法名: get, 用户ID: null, URI: /api/admin/validate/code/get, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:35:14.636",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-6",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: 9cfc61404cb345339f2c05a588b7b203, 类名: AdminLoginController, 方法名: getLoginPic, 用户ID: null, URI: /api/admin/getLoginPic, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:35:14.636",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-15",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"66be7d7958194f58a057d6d3dd17922e","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"get","requestParams":{"request":{"temp":"**********"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/validate/code/get","url":"http://127.0.0.1:20000/api/admin/validate/code/get"},"className":"com.genco.admin.controller.ValidateCodeController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","uri":"/api/admin/validate/code/get"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:35:14.636",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-6",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"9cfc61404cb345339f2c05a588b7b203","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"getLoginPic","requestParams":{"request":{"temp":"**********"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/getLoginPic","url":"http://127.0.0.1:20000/api/admin/getLoginPic"},"className":"com.genco.admin.controller.AdminLoginController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","uri":"/api/admin/getLoginPic"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:35:14.656",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-15",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.ValidateCodeController.get()，prams：[]，cost time：18863100 ns，cost：18 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:35:14.656",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-15",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: 66be7d7958194f58a057d6d3dd17922e, 类名: ValidateCodeController, 方法名: get, 用户ID: null, 是否成功: true, 响应时间: 20ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:35:14.657",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-15",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"66be7d7958194f58a057d6d3dd17922e","requestBody":"未记录","responseTime":20,"methodName":"get","className":"com.genco.admin.controller.ValidateCodeController","isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:35:15.110",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-6",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.AdminLoginController.getLoginPic()，prams：[]，cost time：473186600 ns，cost：473 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:35:15.111",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-6",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: 9cfc61404cb345339f2c05a588b7b203, 类名: AdminLoginController, 方法名: getLoginPic, 用户ID: null, 是否成功: true, 响应时间: 475ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:35:15.111",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-6",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"9cfc61404cb345339f2c05a588b7b203","requestBody":"未记录","responseTime":475,"methodName":"getLoginPic","className":"com.genco.admin.controller.AdminLoginController","isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:35:39.304",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-13",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: 5067f8a1721544adbcc4dfe45aa105a9, 类名: ValidateCodeController, 方法名: get, 用户ID: null, URI: /api/admin/validate/code/get, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:35:39.304",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-13",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"5067f8a1721544adbcc4dfe45aa105a9","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"get","requestParams":{"request":{"temp":"**********"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/validate/code/get","url":"http://127.0.0.1:20000/api/admin/validate/code/get"},"className":"com.genco.admin.controller.ValidateCodeController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","uri":"/api/admin/validate/code/get"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:35:39.304",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-12",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: edd80ded093e4b95a7186fb2ae2dbb61, 类名: AdminLoginController, 方法名: getLoginPic, 用户ID: null, URI: /api/admin/getLoginPic, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:35:39.304",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-12",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"edd80ded093e4b95a7186fb2ae2dbb61","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"getLoginPic","requestParams":{"request":{"temp":"**********"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/getLoginPic","url":"http://127.0.0.1:20000/api/admin/getLoginPic"},"className":"com.genco.admin.controller.AdminLoginController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","uri":"/api/admin/getLoginPic"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:35:39.323",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-13",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.ValidateCodeController.get()，prams：[]，cost time：18502300 ns，cost：18 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:35:39.324",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-13",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: 5067f8a1721544adbcc4dfe45aa105a9, 类名: ValidateCodeController, 方法名: get, 用户ID: null, 是否成功: true, 响应时间: 20ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:35:39.324",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-13",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"5067f8a1721544adbcc4dfe45aa105a9","requestBody":"未记录","responseTime":20,"methodName":"get","className":"com.genco.admin.controller.ValidateCodeController","isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:35:39.789",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-12",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.AdminLoginController.getLoginPic()，prams：[]，cost time：484683600 ns，cost：484 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:35:39.791",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-12",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: edd80ded093e4b95a7186fb2ae2dbb61, 类名: AdminLoginController, 方法名: getLoginPic, 用户ID: null, 是否成功: true, 响应时间: 487ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:35:39.791",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-12",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"edd80ded093e4b95a7186fb2ae2dbb61","requestBody":"未记录","responseTime":487,"methodName":"getLoginPic","className":"com.genco.admin.controller.AdminLoginController","isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:35:59.645",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-5",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: 00989f2e51aa4ef6b8fcb63624335e5d, 类名: AdminLoginController, 方法名: getLoginPic, 用户ID: null, URI: /api/admin/getLoginPic, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:35:59.646",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-3",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: 6fbfe17b978b4873affad610e8a46a85, 类名: ValidateCodeController, 方法名: get, 用户ID: null, URI: /api/admin/validate/code/get, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:35:59.646",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-5",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"00989f2e51aa4ef6b8fcb63624335e5d","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"getLoginPic","requestParams":{"request":{"temp":"**********"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/getLoginPic","url":"http://127.0.0.1:20000/api/admin/getLoginPic"},"className":"com.genco.admin.controller.AdminLoginController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","uri":"/api/admin/getLoginPic"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:35:59.646",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-3",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"6fbfe17b978b4873affad610e8a46a85","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"get","requestParams":{"request":{"temp":"**********"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/validate/code/get","url":"http://127.0.0.1:20000/api/admin/validate/code/get"},"className":"com.genco.admin.controller.ValidateCodeController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","uri":"/api/admin/validate/code/get"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:35:59.655",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-3",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.ValidateCodeController.get()，prams：[]，cost time：8080300 ns，cost：8 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:35:59.656",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-3",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: 6fbfe17b978b4873affad610e8a46a85, 类名: ValidateCodeController, 方法名: get, 用户ID: null, 是否成功: true, 响应时间: 10ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:35:59.656",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-3",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"6fbfe17b978b4873affad610e8a46a85","requestBody":"未记录","responseTime":10,"methodName":"get","className":"com.genco.admin.controller.ValidateCodeController","isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:00.117",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-5",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.AdminLoginController.getLoginPic()，prams：[]，cost time：470272300 ns，cost：470 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:00.118",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-5",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: 00989f2e51aa4ef6b8fcb63624335e5d, 类名: AdminLoginController, 方法名: getLoginPic, 用户ID: null, 是否成功: true, 响应时间: 473ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:00.118",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-5",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"00989f2e51aa4ef6b8fcb63624335e5d","requestBody":"未记录","responseTime":473,"methodName":"getLoginPic","className":"com.genco.admin.controller.AdminLoginController","isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:06.664",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-29",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: 647653e6e2b34734a0ca1ac6a6980023, 类名: ValidateCodeController, 方法名: get, 用户ID: null, URI: /api/admin/validate/code/get, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:06.664",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-18",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: 216903bbda624753b649d9852c5b8f1c, 类名: AdminLoginController, 方法名: getLoginPic, 用户ID: null, URI: /api/admin/getLoginPic, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:06.666",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-29",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"647653e6e2b34734a0ca1ac6a6980023","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"get","requestParams":{"request":{"temp":"**********"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/validate/code/get","url":"http://127.0.0.1:20000/api/admin/validate/code/get"},"className":"com.genco.admin.controller.ValidateCodeController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","uri":"/api/admin/validate/code/get"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:06.666",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-18",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"216903bbda624753b649d9852c5b8f1c","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"getLoginPic","requestParams":{"request":{"temp":"**********"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/getLoginPic","url":"http://127.0.0.1:20000/api/admin/getLoginPic"},"className":"com.genco.admin.controller.AdminLoginController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","uri":"/api/admin/getLoginPic"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:06.675",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-29",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.ValidateCodeController.get()，prams：[]，cost time：9288900 ns，cost：9 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:06.677",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-29",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: 647653e6e2b34734a0ca1ac6a6980023, 类名: ValidateCodeController, 方法名: get, 用户ID: null, 是否成功: true, 响应时间: 13ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:06.677",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-29",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"647653e6e2b34734a0ca1ac6a6980023","requestBody":"未记录","responseTime":13,"methodName":"get","className":"com.genco.admin.controller.ValidateCodeController","isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:07.141",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-18",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.AdminLoginController.getLoginPic()，prams：[]，cost time：475299700 ns，cost：475 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:07.142",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-18",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: 216903bbda624753b649d9852c5b8f1c, 类名: AdminLoginController, 方法名: getLoginPic, 用户ID: null, 是否成功: true, 响应时间: 478ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:07.142",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-18",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"216903bbda624753b649d9852c5b8f1c","requestBody":"未记录","responseTime":478,"methodName":"getLoginPic","className":"com.genco.admin.controller.AdminLoginController","isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:11.198",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-2",
                    "class": "c.g.admin.task.order.OrderTiktokPullTask",
                    "message": "创建TikTok订单拉取初始任务，时间范围：Mon Aug 04 09:36:10 CST 2025 至 Thu Aug 07 09:36:10 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:19.822",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-9",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: 10498440deee4b319e1626a8789648fe, 类名: AdminLoginController, 方法名: SystemAdminLogin, 用户ID: null, URI: /api/admin/login, 方法: POST" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:19.822",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-9",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"10498440deee4b319e1626a8789648fe","method":"POST","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"SystemAdminLogin","requestParams":{"request":{},"method":"POST","request_filter":{},"header":{"sec-fetch-mode":"cors","content-length":"107","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","content-type":"application/json","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/login","url":"http://127.0.0.1:20000/api/admin/login"},"className":"com.genco.admin.controller.AdminLoginController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","uri":"/api/admin/login"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:20.320",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-9",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.AdminLoginController.SystemAdminLogin(SystemAdminLoginRequest,HttpServletRequest)，prams：[SystemAdminLoginRequest(account=admin, pwd=BzX36YjvNR8o, key=fd9863aa8ef9ff3022b5695ce306eaa3, code=6Gjq), SecurityContextHolderAwareRequestWrapper[ org.springframework.security.web.header.HeaderWriterFilter$HeaderWriterRequest@178e29ac]]，cost time：********* ns，cost：492 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:20.320",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-9",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: 10498440deee4b319e1626a8789648fe, 类名: AdminLoginController, 方法名: SystemAdminLogin, 用户ID: null, 是否成功: true, 响应时间: 498ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:20.320",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-9",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"10498440deee4b319e1626a8789648fe","requestBody":"未记录","responseTime":498,"methodName":"SystemAdminLogin","className":"com.genco.admin.controller.AdminLoginController","isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:20.340",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-28",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: f2e02c329c2040a389e27a20745b49fe, 类名: AdminLoginController, 方法名: getAdminInfo, 用户ID: 1, URI: /api/admin/getAdminInfoByToken, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:20.340",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-11",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: c220eae945494bfca284ed61a3833ace, 类名: SystemStoreStaffController, 方法名: getList, 用户ID: 1, URI: /api/admin/system/store/staff/list, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:20.340",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-11",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"c220eae945494bfca284ed61a3833ace","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"getList","requestParams":{"request":{"temp":"1754530580","limit":"9999","page":"1"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"9c01e5ed20784ce7a46a4210aae65538","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/system/store/staff/list","url":"http://127.0.0.1:20000/api/admin/system/store/staff/list"},"className":"com.genco.admin.controller.SystemStoreStaffController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/system/store/staff/list"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:20.340",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-28",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"f2e02c329c2040a389e27a20745b49fe","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"getAdminInfo","requestParams":{"request":{"temp":"1754530580","token":"9c01e5ed20784ce7a46a4210aae65538"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"9c01e5ed20784ce7a46a4210aae65538","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/getAdminInfoByToken","url":"http://127.0.0.1:20000/api/admin/getAdminInfoByToken"},"className":"com.genco.admin.controller.AdminLoginController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/getAdminInfoByToken"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:20.340",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-28",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.AdminLoginController.getAdminInfo()，prams：[]，cost time：104600 ns，cost：0 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:20.340",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-28",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: f2e02c329c2040a389e27a20745b49fe, 类名: AdminLoginController, 方法名: getAdminInfo, 用户ID: 1, 是否成功: true, 响应时间: 0ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:20.340",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-28",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"f2e02c329c2040a389e27a20745b49fe","requestBody":"未记录","responseTime":0,"methodName":"getAdminInfo","className":"com.genco.admin.controller.AdminLoginController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:20.361",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-25",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: e7db62b716ce4834929bde8a6f74dce5, 类名: AdminLoginController, 方法名: getMenus, 用户ID: 1, URI: /api/admin/getMenus, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:20.361",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-25",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"e7db62b716ce4834929bde8a6f74dce5","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"getMenus","requestParams":{"request":{"temp":"1754530580"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"9c01e5ed20784ce7a46a4210aae65538","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/getMenus","url":"http://127.0.0.1:20000/api/admin/getMenus"},"className":"com.genco.admin.controller.AdminLoginController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/getMenus"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:20.436",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-11",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemStoreStaffController.getList(Integer,PageParamRequest)，prams：[0, PageParamRequest(page=1, limit=9999)]，cost time：94201300 ns，cost：94 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:20.436",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-11",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: c220eae945494bfca284ed61a3833ace, 类名: SystemStoreStaffController, 方法名: getList, 用户ID: 1, 是否成功: true, 响应时间: 96ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:20.436",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-11",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"c220eae945494bfca284ed61a3833ace","requestBody":"未记录","responseTime":96,"methodName":"getList","className":"com.genco.admin.controller.SystemStoreStaffController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:20.829",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-25",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.AdminLoginController.getMenus()，prams：[]，cost time：467407600 ns，cost：467 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:20.829",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-25",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: e7db62b716ce4834929bde8a6f74dce5, 类名: AdminLoginController, 方法名: getMenus, 用户ID: 1, 是否成功: true, 响应时间: 476ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:20.829",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-25",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"e7db62b716ce4834929bde8a6f74dce5","requestBody":"未记录","responseTime":476,"methodName":"getMenus","className":"com.genco.admin.controller.AdminLoginController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:20.910",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-22",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: e1d57471cd084928822c635a5460b433, 类名: SystemConfigController, 方法名: justGetUniq, 用户ID: 1, URI: /api/admin/system/config/getuniq, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:20.910",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-21",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: 99b180dbdb154fe187ead4a82f3dedfe, 类名: SystemConfigController, 方法名: justGetUniq, 用户ID: 1, URI: /api/admin/system/config/getuniq, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:20.910",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-17",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: caf8c6de238a460280c8ca698e701807, 类名: SystemConfigController, 方法名: info, 用户ID: 1, URI: /api/admin/system/config/info, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:20.910",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-21",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"99b180dbdb154fe187ead4a82f3dedfe","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"justGetUniq","requestParams":{"request":{"temp":"1754530580","key":"site_logo_square"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"9c01e5ed20784ce7a46a4210aae65538","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/system/config/getuniq","url":"http://127.0.0.1:20000/api/admin/system/config/getuniq"},"className":"com.genco.admin.controller.SystemConfigController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/system/config/getuniq"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:20.910",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-17",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"caf8c6de238a460280c8ca698e701807","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"info","requestParams":{"request":{"formId":"100","temp":"1754530580"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"9c01e5ed20784ce7a46a4210aae65538","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/system/config/info","url":"http://127.0.0.1:20000/api/admin/system/config/info"},"className":"com.genco.admin.controller.SystemConfigController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/system/config/info"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:20.910",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-22",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"e1d57471cd084928822c635a5460b433","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"justGetUniq","requestParams":{"request":{"temp":"1754530580","key":"site_logo_lefttop"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"9c01e5ed20784ce7a46a4210aae65538","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/system/config/getuniq","url":"http://127.0.0.1:20000/api/admin/system/config/getuniq"},"className":"com.genco.admin.controller.SystemConfigController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/system/config/getuniq"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:21.003",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-22",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemConfigController.justGetUniq(String)，prams：[site_logo_lefttop]，cost time：94840900 ns，cost：94 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:21.012",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-22",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: e1d57471cd084928822c635a5460b433, 类名: SystemConfigController, 方法名: justGetUniq, 用户ID: 1, 是否成功: true, 响应时间: 94ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:21.012",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-22",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"e1d57471cd084928822c635a5460b433","requestBody":"未记录","responseTime":94,"methodName":"justGetUniq","className":"com.genco.admin.controller.SystemConfigController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:21.138",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-17",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemConfigController.info(Integer)，prams：[100]，cost time：226770600 ns，cost：226 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:21.141",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-17",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: caf8c6de238a460280c8ca698e701807, 类名: SystemConfigController, 方法名: info, 用户ID: 1, 是否成功: true, 响应时间: 229ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:21.141",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-17",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"caf8c6de238a460280c8ca698e701807","requestBody":"未记录","responseTime":229,"methodName":"info","className":"com.genco.admin.controller.SystemConfigController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:21.325",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-21",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemConfigController.justGetUniq(String)，prams：[site_logo_square]，cost time：413371800 ns，cost：413 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:21.328",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-21",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: 99b180dbdb154fe187ead4a82f3dedfe, 类名: SystemConfigController, 方法名: justGetUniq, 用户ID: 1, 是否成功: true, 响应时间: 418ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:21.328",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-21",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"99b180dbdb154fe187ead4a82f3dedfe","requestBody":"未记录","responseTime":418,"methodName":"justGetUniq","className":"com.genco.admin.controller.SystemConfigController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:24.403",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-15",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: 27ba3524a8364e0995338da16b101904, 类名: SystemGroupDataController, 方法名: getList, 用户ID: 1, URI: /api/admin/system/group/data/list, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:24.404",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-6",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: 7916885e85914254b069b356e8c9809e, 类名: StoreBrandController, 方法名: getBrandList, 用户ID: 1, URI: /api/admin/brand/list, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:24.404",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-15",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"27ba3524a8364e0995338da16b101904","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"getList","requestParams":{"request":{"temp":"1754530584","gid":"74"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"9c01e5ed20784ce7a46a4210aae65538","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/system/group/data/list","url":"http://127.0.0.1:20000/api/admin/system/group/data/list"},"className":"com.genco.admin.controller.SystemGroupDataController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/system/group/data/list"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:24.404",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-6",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"7916885e85914254b069b356e8c9809e","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"getBrandList","requestParams":{"request":{"temp":"1754530584","limit":"20","name":"","page":"1","type":"-1"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"9c01e5ed20784ce7a46a4210aae65538","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/brand/list","url":"http://127.0.0.1:20000/api/admin/brand/list"},"className":"com.genco.admin.controller.StoreBrandController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/brand/list"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:24.810",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-15",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemGroupDataController.getList(SystemGroupDataSearchRequest,PageParamRequest)，prams：[SystemGroupDataSearchRequest(keywords=null, gid=74, status=null), PageParamRequest(page=1, limit=20)]，cost time：409643100 ns，cost：409 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:24.810",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-15",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: 27ba3524a8364e0995338da16b101904, 类名: SystemGroupDataController, 方法名: getList, 用户ID: 1, 是否成功: true, 响应时间: 408ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:24.819",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-15",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"27ba3524a8364e0995338da16b101904","requestBody":"未记录","responseTime":408,"methodName":"getList","className":"com.genco.admin.controller.SystemGroupDataController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:29.436",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-6",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.StoreBrandController.getBrandList(Integer,String,PageParamRequest)，prams：[-1, , PageParamRequest(page=1, limit=20)]，cost time：5032213500 ns，cost：5032 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:29.436",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-6",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: 7916885e85914254b069b356e8c9809e, 类名: StoreBrandController, 方法名: getBrandList, 用户ID: 1, 是否成功: true, 响应时间: 5034ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:29.436",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-6",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"7916885e85914254b069b356e8c9809e","requestBody":"未记录","responseTime":5034,"methodName":"getBrandList","className":"com.genco.admin.controller.StoreBrandController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:37:12.372",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-30",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "开始同步TikTok订单 - pageToken: null, startTime: Mon Aug 04 09:36:28 CST 2025, endTime: Thu Aug 07 09:36:28 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:37:13.058",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-30",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "开始调用TikTok API获取订单数据" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:37:23.500",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-30",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "API调用成功，获取到下一页token: , 开始处理订单数据" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:37:23.730",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-30",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "开始处理 3 个订单" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:37:24.428",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-30",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "订单批量处理完成 - 总计: 3, 新增: 0, 更新: 0, 跳过: 3, 错误: 0" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:37:24.891",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-30",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "TikTok订单同步事务执行完成，下一页token: " }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:37:24.891",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-30",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "TikTok订单同步方法执行完成，返回下一页token: " }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:26.534",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-3",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "开始同步TikTok订单 - pageToken: null, startTime: Mon Aug 04 09:37:29 CST 2025, endTime: Thu Aug 07 09:37:29 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:27.219",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-3",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "开始调用TikTok API获取订单数据" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:27.669",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-3",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "API调用成功，获取到下一页token: , 开始处理订单数据" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:27.894",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-3",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "开始处理 3 个订单" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:28.580",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-3",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "订单批量处理完成 - 总计: 3, 新增: 0, 更新: 0, 跳过: 3, 错误: 0" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:29.030",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-3",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "TikTok订单同步事务执行完成，下一页token: " }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:29.030",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-3",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "TikTok订单同步方法执行完成，返回下一页token: " }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:37.557",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-3",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: 1afd0a057ed44ebc8e5eb243d3300381, 类名: AdminLoginController, 方法名: getAdminInfo, 用户ID: 1, URI: /api/admin/getAdminInfoByToken, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:37.558",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-3",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"1afd0a057ed44ebc8e5eb243d3300381","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"getAdminInfo","requestParams":{"request":{"temp":"1754530717","token":"9c01e5ed20784ce7a46a4210aae65538"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"9c01e5ed20784ce7a46a4210aae65538","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/getAdminInfoByToken","url":"http://127.0.0.1:20000/api/admin/getAdminInfoByToken"},"className":"com.genco.admin.controller.AdminLoginController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/getAdminInfoByToken"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:37.559",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-3",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.AdminLoginController.getAdminInfo()，prams：[]，cost time：91100 ns，cost：0 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:37.561",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-3",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: 1afd0a057ed44ebc8e5eb243d3300381, 类名: AdminLoginController, 方法名: getAdminInfo, 用户ID: 1, 是否成功: true, 响应时间: 4ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:37.562",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-3",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"1afd0a057ed44ebc8e5eb243d3300381","requestBody":"未记录","responseTime":4,"methodName":"getAdminInfo","className":"com.genco.admin.controller.AdminLoginController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:37.582",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-4",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: f8e4063ab91f4ca0bdc14b99def2fa55, 类名: AdminLoginController, 方法名: getMenus, 用户ID: 1, URI: /api/admin/getMenus, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:37.584",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-4",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"f8e4063ab91f4ca0bdc14b99def2fa55","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"getMenus","requestParams":{"request":{"temp":"1754530717"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"9c01e5ed20784ce7a46a4210aae65538","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/getMenus","url":"http://127.0.0.1:20000/api/admin/getMenus"},"className":"com.genco.admin.controller.AdminLoginController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/getMenus"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:37.817",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-4",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.AdminLoginController.getMenus()，prams：[]，cost time：233594700 ns，cost：233 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:37.821",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-4",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: f8e4063ab91f4ca0bdc14b99def2fa55, 类名: AdminLoginController, 方法名: getMenus, 用户ID: 1, 是否成功: true, 响应时间: 239ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:37.821",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-4",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"f8e4063ab91f4ca0bdc14b99def2fa55","requestBody":"未记录","responseTime":239,"methodName":"getMenus","className":"com.genco.admin.controller.AdminLoginController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:38.190",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-16",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: 9ec4a42b18494959b2840b59f07c59fa, 类名: SystemGroupDataController, 方法名: getList, 用户ID: 1, URI: /api/admin/system/group/data/list, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:38.190",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-16",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"9ec4a42b18494959b2840b59f07c59fa","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"getList","requestParams":{"request":{"temp":"1754530718","gid":"74"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"9c01e5ed20784ce7a46a4210aae65538","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/system/group/data/list","url":"http://127.0.0.1:20000/api/admin/system/group/data/list"},"className":"com.genco.admin.controller.SystemGroupDataController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/system/group/data/list"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:38.192",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-28",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: 9520f33bfd744e7b9289694032f04bad, 类名: SystemConfigController, 方法名: justGetUniq, 用户ID: 1, URI: /api/admin/system/config/getuniq, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:38.192",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-27",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: 64326b057ea344a1a9c11b0cf5b28912, 类名: StoreBrandController, 方法名: getBrandList, 用户ID: 1, URI: /api/admin/brand/list, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:38.192",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-28",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"9520f33bfd744e7b9289694032f04bad","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"justGetUniq","requestParams":{"request":{"temp":"1754530718","key":"site_logo_square"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"9c01e5ed20784ce7a46a4210aae65538","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/system/config/getuniq","url":"http://127.0.0.1:20000/api/admin/system/config/getuniq"},"className":"com.genco.admin.controller.SystemConfigController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/system/config/getuniq"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:38.192",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-27",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"64326b057ea344a1a9c11b0cf5b28912","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"getBrandList","requestParams":{"request":{"temp":"1754530718","limit":"20","name":"","page":"1","type":"-1"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"9c01e5ed20784ce7a46a4210aae65538","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/brand/list","url":"http://127.0.0.1:20000/api/admin/brand/list"},"className":"com.genco.admin.controller.StoreBrandController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/brand/list"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:38.193",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-9",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: f11852adb53d4e0d8ecdabca43c4c296, 类名: SystemConfigController, 方法名: justGetUniq, 用户ID: 1, URI: /api/admin/system/config/getuniq, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:38.193",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-9",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"f11852adb53d4e0d8ecdabca43c4c296","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"justGetUniq","requestParams":{"request":{"temp":"1754530718","key":"site_logo_lefttop"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"9c01e5ed20784ce7a46a4210aae65538","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/system/config/getuniq","url":"http://127.0.0.1:20000/api/admin/system/config/getuniq"},"className":"com.genco.admin.controller.SystemConfigController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/system/config/getuniq"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:38.423",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-28",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemConfigController.justGetUniq(String)，prams：[site_logo_square]，cost time：229940000 ns，cost：229 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:38.425",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-28",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: 9520f33bfd744e7b9289694032f04bad, 类名: SystemConfigController, 方法名: justGetUniq, 用户ID: 1, 是否成功: true, 响应时间: 234ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:38.426",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-28",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"9520f33bfd744e7b9289694032f04bad","requestBody":"未记录","responseTime":234,"methodName":"justGetUniq","className":"com.genco.admin.controller.SystemConfigController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:38.479",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-16",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemGroupDataController.getList(SystemGroupDataSearchRequest,PageParamRequest)，prams：[SystemGroupDataSearchRequest(keywords=null, gid=74, status=null), PageParamRequest(page=1, limit=20)]，cost time：286092000 ns，cost：286 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:38.483",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-16",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: 9ec4a42b18494959b2840b59f07c59fa, 类名: SystemGroupDataController, 方法名: getList, 用户ID: 1, 是否成功: true, 响应时间: 291ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:38.483",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-16",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"9ec4a42b18494959b2840b59f07c59fa","requestBody":"未记录","responseTime":291,"methodName":"getList","className":"com.genco.admin.controller.SystemGroupDataController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:38.605",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-9",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemConfigController.justGetUniq(String)，prams：[site_logo_lefttop]，cost time：411514200 ns，cost：411 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:38.608",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-9",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: f11852adb53d4e0d8ecdabca43c4c296, 类名: SystemConfigController, 方法名: justGetUniq, 用户ID: 1, 是否成功: true, 响应时间: 417ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:38.608",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-9",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"f11852adb53d4e0d8ecdabca43c4c296","requestBody":"未记录","responseTime":417,"methodName":"justGetUniq","className":"com.genco.admin.controller.SystemConfigController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:40.040",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-27",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.StoreBrandController.getBrandList(Integer,String,PageParamRequest)，prams：[-1, , PageParamRequest(page=1, limit=20)]，cost time：1847127500 ns，cost：1847 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:40.044",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-27",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: 64326b057ea344a1a9c11b0cf5b28912, 类名: StoreBrandController, 方法名: getBrandList, 用户ID: 1, 是否成功: true, 响应时间: 1852ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:40.044",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-27",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"64326b057ea344a1a9c11b0cf5b28912","requestBody":"未记录","responseTime":1852,"methodName":"getBrandList","className":"com.genco.admin.controller.StoreBrandController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:39:29.924",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-18",
                    "class": "c.g.admin.task.order.OrderTiktokPullTask",
                    "message": "创建TikTok订单拉取初始任务，时间范围：Mon Aug 04 09:39:29 CST 2025 至 Thu Aug 07 09:39:29 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:39:39.021",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-19",
                    "class": "c.g.a.task.tiktok.TiktokTokenRefreshTask",
                    "message": "---TiktokTokenRefreshTask task------produce Data with fixed rate task: Execution Time - Thu Aug 07 09:39:39 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:39:39.100",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-19",
                    "class": "c.g.a.task.tiktok.TiktokTokenRefreshTask",
                    "message": "Tiktok access_token 未到刷新时间，当前时间: 1754530779，过期时间: 1781838424" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:40:30.338",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-20",
                    "class": "c.g.admin.task.order.OrderTiktokPullTask",
                    "message": "创建TikTok订单拉取初始任务，时间范围：Mon Aug 04 09:40:30 CST 2025 至 Thu Aug 07 09:40:30 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:41:30.758",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-11",
                    "class": "c.g.admin.task.order.OrderTiktokPullTask",
                    "message": "创建TikTok订单拉取初始任务，时间范围：Mon Aug 04 09:41:30 CST 2025 至 Thu Aug 07 09:41:30 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:42:31.185",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-23",
                    "class": "c.g.admin.task.order.OrderTiktokPullTask",
                    "message": "创建TikTok订单拉取初始任务，时间范围：Mon Aug 04 09:42:31 CST 2025 至 Thu Aug 07 09:42:31 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:43:04.176",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-17",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: 62285bba695547778ea343fb17d57572, 类名: AdminLoginController, 方法名: getAdminInfo, 用户ID: 1, URI: /api/admin/getAdminInfoByToken, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:43:04.177",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-17",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"62285bba695547778ea343fb17d57572","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"getAdminInfo","requestParams":{"request":{"temp":"1754530984","token":"9c01e5ed20784ce7a46a4210aae65538"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"9c01e5ed20784ce7a46a4210aae65538","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/getAdminInfoByToken","url":"http://127.0.0.1:20000/api/admin/getAdminInfoByToken"},"className":"com.genco.admin.controller.AdminLoginController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/getAdminInfoByToken"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:43:04.178",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-17",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.AdminLoginController.getAdminInfo()，prams：[]，cost time：107600 ns，cost：0 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:43:04.181",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-17",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: 62285bba695547778ea343fb17d57572, 类名: AdminLoginController, 方法名: getAdminInfo, 用户ID: 1, 是否成功: true, 响应时间: 4ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:43:04.181",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-17",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"62285bba695547778ea343fb17d57572","requestBody":"未记录","responseTime":4,"methodName":"getAdminInfo","className":"com.genco.admin.controller.AdminLoginController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:43:04.200",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-19",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: 5529405f26f34b558d51dd3ee55e2488, 类名: AdminLoginController, 方法名: getMenus, 用户ID: 1, URI: /api/admin/getMenus, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:43:04.202",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-19",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"5529405f26f34b558d51dd3ee55e2488","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"getMenus","requestParams":{"request":{"temp":"1754530984"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"9c01e5ed20784ce7a46a4210aae65538","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/getMenus","url":"http://127.0.0.1:20000/api/admin/getMenus"},"className":"com.genco.admin.controller.AdminLoginController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/getMenus"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:43:04.285",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-19",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.AdminLoginController.getMenus()，prams：[]，cost time：83079200 ns，cost：83 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:43:04.288",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-19",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: 5529405f26f34b558d51dd3ee55e2488, 类名: AdminLoginController, 方法名: getMenus, 用户ID: 1, 是否成功: true, 响应时间: 88ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:43:04.288",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-19",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"5529405f26f34b558d51dd3ee55e2488","requestBody":"未记录","responseTime":88,"methodName":"getMenus","className":"com.genco.admin.controller.AdminLoginController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:43:04.652",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-13",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: fda91dccf19844c8aee2ef3a366598f5, 类名: SystemConfigController, 方法名: justGetUniq, 用户ID: 1, URI: /api/admin/system/config/getuniq, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:43:04.653",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-13",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"fda91dccf19844c8aee2ef3a366598f5","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"justGetUniq","requestParams":{"request":{"temp":"1754530984","key":"site_logo_lefttop"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"9c01e5ed20784ce7a46a4210aae65538","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/system/config/getuniq","url":"http://127.0.0.1:20000/api/admin/system/config/getuniq"},"className":"com.genco.admin.controller.SystemConfigController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/system/config/getuniq"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:43:04.653",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-10",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: 7e6831ffe8134590a536bbff051558f4, 类名: SystemGroupDataController, 方法名: getList, 用户ID: 1, URI: /api/admin/system/group/data/list, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:43:04.653",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-7",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: 4bfa420c1cf4455bb0a281a65777ca5c, 类名: SystemConfigController, 方法名: justGetUniq, 用户ID: 1, URI: /api/admin/system/config/getuniq, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:43:04.653",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-12",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: 7333979ec8b2476683ae4ca8581db1c7, 类名: StoreBrandController, 方法名: getBrandList, 用户ID: 1, URI: /api/admin/brand/list, 方法: GET" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:43:04.653",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-7",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"4bfa420c1cf4455bb0a281a65777ca5c","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"justGetUniq","requestParams":{"request":{"temp":"1754530984","key":"site_logo_square"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"9c01e5ed20784ce7a46a4210aae65538","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/system/config/getuniq","url":"http://127.0.0.1:20000/api/admin/system/config/getuniq"},"className":"com.genco.admin.controller.SystemConfigController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/system/config/getuniq"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:43:04.653",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-10",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"7e6831ffe8134590a536bbff051558f4","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"getList","requestParams":{"request":{"temp":"1754530984","gid":"74"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"9c01e5ed20784ce7a46a4210aae65538","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/system/group/data/list","url":"http://127.0.0.1:20000/api/admin/system/group/data/list"},"className":"com.genco.admin.controller.SystemGroupDataController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/system/group/data/list"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:43:04.653",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-12",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {"traceId":"7333979ec8b2476683ae4ca8581db1c7","method":"GET","requestBody":"未记录","clientIp":"127.0.0.1","methodName":"getBrandList","requestParams":{"request":{"temp":"1754530984","limit":"20","name":"","page":"1","type":"-1"},"method":"GET","request_filter":{},"header":{"sec-fetch-mode":"cors","referer":"http://localhost:9527/","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"9\", \"Not?A_Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:20000","connection":"keep-alive","authori-zation":"9c01e5ed20784ce7a46a4210aae65538","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","sec-fetch-dest":"empty"},"uri":"/api/admin/brand/list","url":"http://127.0.0.1:20000/api/admin/brand/list"},"className":"com.genco.admin.controller.StoreBrandController","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit","userId":1,"uri":"/api/admin/brand/list"}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:43:04.733",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-7",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemConfigController.justGetUniq(String)，prams：[site_logo_square]，cost time：78962200 ns，cost：78 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:43:04.735",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-7",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: 4bfa420c1cf4455bb0a281a65777ca5c, 类名: SystemConfigController, 方法名: justGetUniq, 用户ID: 1, 是否成功: true, 响应时间: 82ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:43:04.736",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-7",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"4bfa420c1cf4455bb0a281a65777ca5c","requestBody":"未记录","responseTime":82,"methodName":"justGetUniq","className":"com.genco.admin.controller.SystemConfigController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:43:04.957",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-10",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemGroupDataController.getList(SystemGroupDataSearchRequest,PageParamRequest)，prams：[SystemGroupDataSearchRequest(keywords=null, gid=74, status=null), PageParamRequest(page=1, limit=20)]，cost time：302695100 ns，cost：302 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:43:04.960",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-10",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: 7e6831ffe8134590a536bbff051558f4, 类名: SystemGroupDataController, 方法名: getList, 用户ID: 1, 是否成功: true, 响应时间: 307ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:43:04.960",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-10",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"7e6831ffe8134590a536bbff051558f4","requestBody":"未记录","responseTime":307,"methodName":"getList","className":"com.genco.admin.controller.SystemGroupDataController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:43:05.066",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-13",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemConfigController.justGetUniq(String)，prams：[site_logo_lefttop]，cost time：413116100 ns，cost：413 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:43:05.069",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-13",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: fda91dccf19844c8aee2ef3a366598f5, 类名: SystemConfigController, 方法名: justGetUniq, 用户ID: 1, 是否成功: true, 响应时间: 418ms, 状态码: 200" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:43:05.069",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-13",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {"traceId":"fda91dccf19844c8aee2ef3a366598f5","requestBody":"未记录","responseTime":418,"methodName":"justGetUniq","className":"com.genco.admin.controller.SystemConfigController","userId":1,"isSuccess":true,"statusCode":200}" }
                    
