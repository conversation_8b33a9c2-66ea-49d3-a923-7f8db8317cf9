import Vue from 'vue'
import Element from 'element-ui'
import zhCNLocale from 'element-ui/lib/locale/lang/zh-CN'
import enLocale from 'element-ui/lib/locale/lang/en'
import idLocale from 'element-ui/lib/locale/lang/id'

// Element UI 语言包映射
const elementLocales = {
  'zh-CN': zhCNLocale,
  'en': enLocale,
  'id': idLocale
}

/**
 * 切换 Element UI 语言包
 * @param {string} locale - 语言代码 ('zh-CN', 'en', 'id')
 */
export function switchElementLocale(locale) {
  const elementLocale = elementLocales[locale] || zhCNLocale
  
  // 动态切换 Element UI 语言包
  Element.locale(elementLocale)
}

/**
 * 获取 Element UI 语言包
 * @param {string} locale - 语言代码
 * @returns {Object} Element UI 语言包对象
 */
export function getElementLocale(locale) {
  return elementLocales[locale] || zhCNLocale
}

/**
 * 获取所有支持的语言包
 * @returns {Object} 所有语言包映射
 */
export function getAllElementLocales() {
  return elementLocales
}
