import Vue from 'vue'
import VueI18n from 'vue-i18n'
import zhCN from './lang/zh-CN'
import en from './lang/en'
import id from './lang/id'
import { switchElementLocale } from './utils/elementLocale'

Vue.use(VueI18n)

const messages = {
  'zh-CN': zhCN,
  en: en,
  id: id
}

const i18n = new VueI18n({
 locale: localStorage.getItem('locale') || 'zh-CN',
  fallbackLocale: 'zh-CN',
  messages
})

// 初始化时设置 Element UI 语言包
switchElementLocale(i18n.locale)

// 监听语言切换，同步更新 Element UI 语言包
i18n.vm.$watch('locale', (newLocale) => {
  switchElementLocale(newLocale)
})

export default i18n
