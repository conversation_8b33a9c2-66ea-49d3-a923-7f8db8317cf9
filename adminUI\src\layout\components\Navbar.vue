<template>
  <div class="navbar">
    <hamburger
      id="hamburger-container"
      :is-active="sidebar.opened"
      class="hamburger-container"
      @toggleClick="toggleSideBar"
    />
    <breadcrumb id="breadcrumb-container" class="breadcrumb-container" />
    <div class="right-menu">
      <template v-if="device !== 'mobile'">
<!--         <search id="header-search" class="right-menu-item" />--> 
        <screenfull id="screenfull" class="right-menu-item hover-effect" />
      </template>
      <el-dropdown
        class="avatar-container right-menu-item hover-effect"
        trigger="click"
      >
        <div class="avatar-wrapper">
          语言：{{ langLabel(nowLanguage)
          }}<i class="el-icon-arrow-down el-icon--right"></i>
        </div>
        <el-dropdown-menu slot="dropdown">
          <template v-for="lang in availableLanguages">
            <el-dropdown-item
              :key="lang"
              v-if="lang !== nowLanguage"
              @click.native="toggleLang(lang)"
            >
              {{ langLabel(lang) }}
            </el-dropdown-item>
          </template>
        </el-dropdown-menu>
      </el-dropdown>
      <el-dropdown
        class="avatar-container right-menu-item hover-effect"
        trigger="click"
      >
        <div class="avatar-wrapper">
          {{ JavaInfo.realName
          }}<i class="el-icon-arrow-down el-icon--right"></i>
        </div>
        <el-dropdown-menu slot="dropdown">
          <router-link to="/">
            <el-dropdown-item>{{ $t("navbar.home") }}</el-dropdown-item>
          </router-link>
          <router-link :to="{ path: '/maintain/user' }" v-if="!isPhone">
            <el-dropdown-item>{{
              $t("navbar.profile")
            }}</el-dropdown-item>
          </router-link>

          <el-dropdown-item @click.native="logout">
            <span>{{ $t("navbar.logout") }}</span>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import Breadcrumb from "@/components/Breadcrumb";
import Hamburger from "@/components/Hamburger";
import Screenfull from "@/components/Screenfull";
import Search from "@/components/HeaderSearch";
import { unbindApi } from "@/api/wxApi";
import Cookies from "js-cookie";
import { switchElementLocale } from "@/utils/elementLocale";
export default {
  components: {
    Breadcrumb,
    Hamburger,
    Screenfull,
    Search
  },
  data() {
    return {
      isPhone: this.$wechat.isPhone(),
      JavaInfo: JSON.parse(Cookies.get("JavaInfo")),
      languageLabels: {
        "zh-CN": "Chinese(中文)",
        en: "English(英文)",
        id: "Indonesian(印尼语)"
      },
      nowLanguage: localStorage.getItem("locale") || "zh-CN"
    };
  },
  computed: {
    ...mapGetters(["sidebar", "avatar", "device"]),
    availableLanguages() {
      return Object.keys(this.languageLabels);
    }
  },
  methods: {
    toggleSideBar() {
      this.$store.dispatch("app/toggleSideBar");
    },
    toggleLang(lang) {
      this.nowLanguage = lang;
      this.$i18n.locale = lang;
      localStorage.setItem("locale", lang);
      // 同步切换 Element UI 语言包
      switchElementLocale(lang);
    },
    langLabel(lang) {
      return this.languageLabels[lang] || lang;
    },
    async logout() {
      await this.$store.dispatch("user/logout");
      this.$router.push(`/login?redirect=${this.$route.fullPath}`);
    }
  }
};
</script>

<style lang="scss" scoped>
.navbar {
  height: 50px;
  overflow: hidden;
  position: relative;
  background: #fff;
  // box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);

  .hamburger-container {
    line-height: 46px;
    height: 100%;
    float: left;
    cursor: pointer;
    transition: background 0.3s;
    -webkit-tap-highlight-color: transparent;

    &:hover {
      background: rgba(0, 0, 0, 0.025);
    }
  }

  .breadcrumb-container {
    float: left;
  }

  .topmenu-container {
    position: absolute;
    left: 50px;
  }

  .errLog-container {
    display: inline-block;
    vertical-align: top;
  }

  .right-menu {
    float: right;
    height: 100%;
    line-height: 50px;

    &:focus {
      outline: none;
    }

    .right-menu-item {
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      font-size: 18px;
      color: #5a5e66;
      vertical-align: text-bottom;

      &.hover-effect {
        cursor: pointer;
        transition: background 0.3s;

        &:hover {
          background: rgba(0, 0, 0, 0.025);
        }
      }
    }

    .avatar-container {
      margin-right: 30px;

      .avatar-wrapper {
        position: relative;
        font-size: 14px;
        .user-avatar {
          cursor: pointer;
          width: 40px;
          height: 40px;
          border-radius: 10px;
        }

        .el-icon-caret-bottom {
          cursor: pointer;
          position: absolute;
          right: -20px;
          top: 25px;
          font-size: 12px;
        }
      }
    }
  }
}
</style>
