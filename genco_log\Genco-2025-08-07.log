{"@timestamp":"2025-08-07T09:19:17.129+08:00","@version":"1","message":"Application started with classpath: [file:/D:/Java/jre/lib/charsets.jar, file:/D:/Java/jre/lib/deploy.jar, file:/D:/Java/jre/lib/ext/access-bridge-64.jar, file:/D:/Java/jre/lib/ext/cldrdata.jar, file:/D:/Java/jre/lib/ext/dnsns.jar, file:/D:/Java/jre/lib/ext/jaccess.jar, file:/D:/Java/jre/lib/ext/localedata.jar, file:/D:/Java/jre/lib/ext/nashorn.jar, file:/D:/Java/jre/lib/ext/sunec.jar, file:/D:/Java/jre/lib/ext/sunjce_provider.jar, file:/D:/Java/jre/lib/ext/sunmscapi.jar, file:/D:/Java/jre/lib/ext/sunpkcs11.jar, file:/D:/Java/jre/lib/ext/zipfs.jar, file:/D:/Java/jre/lib/javaws.jar, file:/D:/Java/jre/lib/jce.jar, file:/D:/Java/jre/lib/jfr.jar, file:/D:/Java/jre/lib/jsse.jar, file:/D:/Java/jre/lib/management-agent.jar, file:/D:/Java/jre/lib/plugin.jar, file:/D:/Java/jre/lib/resources.jar, file:/D:/Java/jre/lib/rt.jar, file:/C:/Users/<USER>/Desktop/shop/api/genco-admin/target/classes/, file:/C:/Users/<USER>/Desktop/shop/api/genco-service/target/classes/, file:/C:/Users/<USER>/Desktop/shop/api/genco-common/target/classes/, file:/D:/MavenRepository/javax/servlet/javax.servlet-api/4.0.1/javax.servlet-api-4.0.1.jar, file:/D:/MavenRepository/javax/servlet/jstl/1.2/jstl-1.2.jar, file:/D:/MavenRepository/org/apache/tomcat/embed/tomcat-embed-jasper/9.0.33/tomcat-embed-jasper-9.0.33.jar, file:/D:/MavenRepository/org/apache/tomcat/embed/tomcat-embed-core/9.0.33/tomcat-embed-core-9.0.33.jar, file:/D:/MavenRepository/org/apache/tomcat/tomcat-annotations-api/9.0.33/tomcat-annotations-api-9.0.33.jar, file:/D:/MavenRepository/org/apache/tomcat/embed/tomcat-embed-el/9.0.33/tomcat-embed-el-9.0.33.jar, file:/D:/MavenRepository/org/eclipse/jdt/ecj/3.18.0/ecj-3.18.0.jar, file:/D:/MavenRepository/org/apache/tomcat/tomcat-jsp-api/9.0.33/tomcat-jsp-api-9.0.33.jar, file:/D:/MavenRepository/org/apache/tomcat/tomcat-el-api/9.0.33/tomcat-el-api-9.0.33.jar, file:/D:/MavenRepository/org/apache/tomcat/tomcat-servlet-api/9.0.33/tomcat-servlet-api-9.0.33.jar, file:/D:/MavenRepository/commons-configuration/commons-configuration/1.10/commons-configuration-1.10.jar, file:/D:/MavenRepository/commons-lang/commons-lang/2.6/commons-lang-2.6.jar, file:/D:/MavenRepository/commons-logging/commons-logging/1.1.1/commons-logging-1.1.1.jar, file:/D:/MavenRepository/org/apache/velocity/velocity/1.7/velocity-1.7.jar, file:/D:/MavenRepository/commons-collections/commons-collections/3.2.1/commons-collections-3.2.1.jar, file:/D:/MavenRepository/com/alibaba/fastjson/1.2.83/fastjson-1.2.83.jar, file:/D:/MavenRepository/com/alibaba/druid/1.1.20/druid-1.1.20.jar, file:/D:/MavenRepository/mysql/mysql-connector-java/8.0.29/mysql-connector-java-8.0.29.jar, file:/D:/MavenRepository/com/google/protobuf/protobuf-java/3.19.4/protobuf-java-3.19.4.jar, file:/D:/MavenRepository/org/projectlombok/lombok/1.18.12/lombok-1.18.12.jar, file:/D:/MavenRepository/com/baomidou/mybatis-plus-boot-starter/3.3.1/mybatis-plus-boot-starter-3.3.1.jar, file:/D:/MavenRepository/com/baomidou/mybatis-plus/3.3.1/mybatis-plus-3.3.1.jar, file:/D:/MavenRepository/com/baomidou/mybatis-plus-extension/3.3.1/mybatis-plus-extension-3.3.1.jar, file:/D:/MavenRepository/com/baomidou/mybatis-plus-core/3.3.1/mybatis-plus-core-3.3.1.jar, file:/D:/MavenRepository/com/baomidou/mybatis-plus-annotation/3.3.1/mybatis-plus-annotation-3.3.1.jar, file:/D:/MavenRepository/com/baomidou/mybatis-plus-generator/3.3.1/mybatis-plus-generator-3.3.1.jar, file:/D:/MavenRepository/io/swagger/swagger-annotations/1.5.21/swagger-annotations-1.5.21.jar, file:/D:/MavenRepository/io/springfox/springfox-swagger2/2.9.2/springfox-swagger2-2.9.2.jar, file:/D:/MavenRepository/io/springfox/springfox-spi/2.9.2/springfox-spi-2.9.2.jar, file:/D:/MavenRepository/io/springfox/springfox-core/2.9.2/springfox-core-2.9.2.jar, file:/D:/MavenRepository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar, file:/D:/MavenRepository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar, file:/D:/MavenRepository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar, file:/D:/MavenRepository/com/google/guava/guava/20.0/guava-20.0.jar, file:/D:/MavenRepository/com/fasterxml/classmate/1.5.1/classmate-1.5.1.jar, file:/D:/MavenRepository/org/springframework/plugin/spring-plugin-core/1.2.0.RELEASE/spring-plugin-core-1.2.0.RELEASE.jar, file:/D:/MavenRepository/org/springframework/plugin/spring-plugin-metadata/1.2.0.RELEASE/spring-plugin-metadata-1.2.0.RELEASE.jar, file:/D:/MavenRepository/org/mapstruct/mapstruct/1.2.0.Final/mapstruct-1.2.0.Final.jar, file:/D:/MavenRepository/io/swagger/swagger-models/1.5.22/swagger-models-1.5.22.jar, file:/D:/MavenRepository/com/fasterxml/jackson/core/jackson-annotations/2.10.3/jackson-annotations-2.10.3.jar, file:/D:/MavenRepository/com/github/xiaoymin/swagger-bootstrap-ui/1.9.3/swagger-bootstrap-ui-1.9.3.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-autoconfigure/2.2.6.RELEASE/spring-boot-autoconfigure-2.2.6.RELEASE.jar, file:/D:/MavenRepository/ch/qos/logback/logback-classic/1.2.3/logback-classic-1.2.3.jar, file:/D:/MavenRepository/ch/qos/logback/logback-core/1.2.3/logback-core-1.2.3.jar, file:/D:/MavenRepository/net/logstash/logback/logstash-logback-encoder/5.3/logstash-logback-encoder-5.3.jar, file:/D:/MavenRepository/com/fasterxml/jackson/core/jackson-databind/2.10.3/jackson-databind-2.10.3.jar, file:/D:/MavenRepository/com/fasterxml/jackson/core/jackson-core/2.10.3/jackson-core-2.10.3.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter-web/2.2.6.RELEASE/spring-boot-starter-web-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter-json/2.2.6.RELEASE/spring-boot-starter-json-2.2.6.RELEASE.jar, file:/D:/MavenRepository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.10.3/jackson-datatype-jdk8-2.10.3.jar, file:/D:/MavenRepository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.10.3/jackson-datatype-jsr310-2.10.3.jar, file:/D:/MavenRepository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.10.3/jackson-module-parameter-names-2.10.3.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter-tomcat/2.2.6.RELEASE/spring-boot-starter-tomcat-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/apache/tomcat/embed/tomcat-embed-websocket/9.0.33/tomcat-embed-websocket-9.0.33.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter-validation/2.2.6.RELEASE/spring-boot-starter-validation-2.2.6.RELEASE.jar, file:/D:/MavenRepository/jakarta/validation/jakarta.validation-api/2.0.2/jakarta.validation-api-2.0.2.jar, file:/D:/MavenRepository/org/hibernate/validator/hibernate-validator/6.0.18.Final/hibernate-validator-6.0.18.Final.jar, file:/D:/MavenRepository/org/jboss/logging/jboss-logging/3.4.1.Final/jboss-logging-3.4.1.Final.jar, file:/D:/MavenRepository/org/springframework/spring-web/5.2.5.RELEASE/spring-web-5.2.5.RELEASE.jar, file:/D:/MavenRepository/org/springframework/spring-webmvc/5.2.5.RELEASE/spring-webmvc-5.2.5.RELEASE.jar, file:/D:/MavenRepository/org/springframework/spring-expression/5.2.5.RELEASE/spring-expression-5.2.5.RELEASE.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter-data-redis/2.2.0.RELEASE/spring-boot-starter-data-redis-2.2.0.RELEASE.jar, file:/D:/MavenRepository/org/springframework/data/spring-data-redis/2.2.6.RELEASE/spring-data-redis-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/springframework/data/spring-data-keyvalue/2.2.6.RELEASE/spring-data-keyvalue-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/springframework/spring-tx/5.2.5.RELEASE/spring-tx-5.2.5.RELEASE.jar, file:/D:/MavenRepository/org/springframework/spring-oxm/5.2.5.RELEASE/spring-oxm-5.2.5.RELEASE.jar, file:/D:/MavenRepository/org/springframework/spring-context-support/5.2.5.RELEASE/spring-context-support-5.2.5.RELEASE.jar, file:/D:/MavenRepository/io/lettuce/lettuce-core/5.2.2.RELEASE/lettuce-core-5.2.2.RELEASE.jar, file:/D:/MavenRepository/io/netty/netty-common/4.1.48.Final/netty-common-4.1.48.Final.jar, file:/D:/MavenRepository/io/netty/netty-handler/4.1.48.Final/netty-handler-4.1.48.Final.jar, file:/D:/MavenRepository/io/netty/netty-resolver/4.1.48.Final/netty-resolver-4.1.48.Final.jar, file:/D:/MavenRepository/io/netty/netty-buffer/4.1.48.Final/netty-buffer-4.1.48.Final.jar, file:/D:/MavenRepository/io/netty/netty-codec/4.1.48.Final/netty-codec-4.1.48.Final.jar, file:/D:/MavenRepository/io/netty/netty-transport/4.1.48.Final/netty-transport-4.1.48.Final.jar, file:/D:/MavenRepository/io/projectreactor/reactor-core/3.3.4.RELEASE/reactor-core-3.3.4.RELEASE.jar, file:/D:/MavenRepository/org/reactivestreams/reactive-streams/1.0.3/reactive-streams-1.0.3.jar, file:/D:/MavenRepository/redis/clients/jedis/3.1.0/jedis-3.1.0.jar, file:/D:/MavenRepository/org/apache/commons/commons-pool2/2.7.0/commons-pool2-2.7.0.jar, file:/D:/MavenRepository/com/github/pagehelper/pagehelper-spring-boot-starter/1.2.5/pagehelper-spring-boot-starter-1.2.5.jar, file:/D:/MavenRepository/com/github/pagehelper/pagehelper-spring-boot-autoconfigure/1.2.5/pagehelper-spring-boot-autoconfigure-1.2.5.jar, file:/D:/MavenRepository/com/github/pagehelper/pagehelper/5.1.4/pagehelper-5.1.4.jar, file:/D:/MavenRepository/com/github/jsqlparser/jsqlparser/1.0/jsqlparser-1.0.jar, file:/D:/MavenRepository/javax/validation/validation-api/1.1.0.Final/validation-api-1.1.0.Final.jar, file:/D:/MavenRepository/org/springframework/data/spring-data-commons/2.2.6.RELEASE/spring-data-commons-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/springframework/spring-beans/5.2.5.RELEASE/spring-beans-5.2.5.RELEASE.jar, file:/D:/MavenRepository/cn/hutool/hutool-all/4.5.7/hutool-all-4.5.7.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter-actuator/2.2.6.RELEASE/spring-boot-starter-actuator-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-actuator-autoconfigure/2.2.6.RELEASE/spring-boot-actuator-autoconfigure-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-actuator/2.2.6.RELEASE/spring-boot-actuator-2.2.6.RELEASE.jar, file:/D:/MavenRepository/io/micrometer/micrometer-core/1.3.6/micrometer-core-1.3.6.jar, file:/D:/MavenRepository/org/hdrhistogram/HdrHistogram/2.1.11/HdrHistogram-2.1.11.jar, file:/D:/MavenRepository/org/latencyutils/LatencyUtils/2.0.3/LatencyUtils-2.0.3.jar, file:/D:/MavenRepository/org/apache/httpcomponents/httpclient/4.5.6/httpclient-4.5.6.jar, file:/D:/MavenRepository/org/apache/httpcomponents/httpcore/4.4.13/httpcore-4.4.13.jar, file:/D:/MavenRepository/commons-codec/commons-codec/1.13/commons-codec-1.13.jar, file:/D:/MavenRepository/org/apache/commons/commons-lang3/3.5/commons-lang3-3.5.jar, file:/D:/MavenRepository/org/apache/poi/poi/3.17/poi-3.17.jar, file:/D:/MavenRepository/org/apache/commons/commons-collections4/4.1/commons-collections4-4.1.jar, file:/D:/MavenRepository/org/apache/poi/poi-ooxml/3.17/poi-ooxml-3.17.jar, file:/D:/MavenRepository/org/apache/poi/poi-ooxml-schemas/3.17/poi-ooxml-schemas-3.17.jar, file:/D:/MavenRepository/org/apache/xmlbeans/xmlbeans/2.6.0/xmlbeans-2.6.0.jar, file:/D:/MavenRepository/com/github/virtuald/curvesapi/1.04/curvesapi-1.04.jar, file:/D:/MavenRepository/commons-fileupload/commons-fileupload/1.3.3/commons-fileupload-1.3.3.jar, file:/D:/MavenRepository/commons-io/commons-io/2.4/commons-io-2.4.jar, file:/D:/MavenRepository/net/coobird/thumbnailator/0.4.8/thumbnailator-0.4.8.jar, file:/D:/MavenRepository/com/aliyun/oss/aliyun-sdk-oss/3.5.0/aliyun-sdk-oss-3.5.0.jar, file:/D:/MavenRepository/org/jdom/jdom/1.1/jdom-1.1.jar, file:/D:/MavenRepository/org/codehaus/jettison/jettison/1.1/jettison-1.1.jar, file:/D:/MavenRepository/stax/stax-api/1.0.1/stax-api-1.0.1.jar, file:/D:/MavenRepository/com/aliyun/aliyun-java-sdk-core/3.4.0/aliyun-java-sdk-core-3.4.0.jar, file:/D:/MavenRepository/com/aliyun/aliyun-java-sdk-ram/3.0.0/aliyun-java-sdk-ram-3.0.0.jar, file:/D:/MavenRepository/com/aliyun/aliyun-java-sdk-sts/3.0.0/aliyun-java-sdk-sts-3.0.0.jar, file:/D:/MavenRepository/com/aliyun/aliyun-java-sdk-ecs/4.2.0/aliyun-java-sdk-ecs-4.2.0.jar, file:/D:/MavenRepository/com/qcloud/cos_api/5.6.22/cos_api-5.6.22.jar, file:/D:/MavenRepository/joda-time/joda-time/2.10.5/joda-time-2.10.5.jar, file:/D:/MavenRepository/org/bouncycastle/bcprov-jdk15on/1.64/bcprov-jdk15on-1.64.jar, file:/D:/MavenRepository/com/qiniu/qiniu-java-sdk/7.2.28/qiniu-java-sdk-7.2.28.jar, file:/D:/MavenRepository/com/google/code/gson/gson/2.8.6/gson-2.8.6.jar, file:/D:/MavenRepository/dom4j/dom4j/1.6.1/dom4j-1.6.1.jar, file:/D:/MavenRepository/xml-apis/xml-apis/1.0.b2/xml-apis-1.0.b2.jar, file:/D:/MavenRepository/com/thoughtworks/xstream/xstream/1.4.18/xstream-1.4.18.jar, file:/D:/MavenRepository/io/github/x-stream/mxparser/1.2.2/mxparser-1.2.2.jar, file:/D:/MavenRepository/xmlpull/xmlpull/1.1.3.1/xmlpull-1.1.3.1.jar, file:/D:/MavenRepository/org/mongodb/mongodb-driver-core/3.8.2/mongodb-driver-core-3.8.2.jar, file:/D:/MavenRepository/org/mongodb/bson/3.11.2/bson-3.11.2.jar, file:/D:/MavenRepository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar, file:/D:/MavenRepository/org/apache/httpcomponents/httpmime/4.5.2/httpmime-4.5.2.jar, file:/D:/MavenRepository/com/google/zxing/core/3.3.3/core-3.3.3.jar, file:/D:/MavenRepository/com/google/zxing/javase/3.3.3/javase-3.3.3.jar, file:/D:/MavenRepository/com/beust/jcommander/1.72/jcommander-1.72.jar, file:/D:/MavenRepository/com/github/jai-imageio/jai-imageio-core/1.4.0/jai-imageio-core-1.4.0.jar, file:/D:/MavenRepository/com/belerweb/pinyin4j/2.5.0/pinyin4j-2.5.0.jar, file:/D:/MavenRepository/io/jsonwebtoken/jjwt/0.9.1/jjwt-0.9.1.jar, file:/D:/MavenRepository/com/auth0/jwks-rsa/0.9.0/jwks-rsa-0.9.0.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter-security/2.2.6.RELEASE/spring-boot-starter-security-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/springframework/security/spring-security-config/5.2.2.RELEASE/spring-security-config-5.2.2.RELEASE.jar, file:/D:/MavenRepository/org/springframework/security/spring-security-core/5.2.2.RELEASE/spring-security-core-5.2.2.RELEASE.jar, file:/D:/MavenRepository/org/springframework/security/spring-security-web/5.2.2.RELEASE/spring-security-web-5.2.2.RELEASE.jar, file:/D:/MavenRepository/com/tiktokshop/open-sdk-java/1.0.0/open-sdk-java-1.0.0.jar, file:/D:/MavenRepository/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar, file:/D:/MavenRepository/io/gsonfire/gson-fire/1.9.0/gson-fire-1.9.0.jar, file:/D:/MavenRepository/org/openapitools/jackson-databind-nullable/0.2.6/jackson-databind-nullable-0.2.6.jar, file:/D:/MavenRepository/javax/ws/rs/jsr311-api/1.1.1/jsr311-api-1.1.1.jar, file:/D:/MavenRepository/javax/ws/rs/javax.ws.rs-api/2.1.1/javax.ws.rs-api-2.1.1.jar, file:/D:/MavenRepository/com/squareup/okhttp3/okhttp/4.11.0/okhttp-4.11.0.jar, file:/D:/MavenRepository/com/squareup/okio/okio/3.2.0/okio-3.2.0.jar, file:/D:/MavenRepository/com/squareup/okio/okio-jvm/3.2.0/okio-jvm-3.2.0.jar, file:/D:/MavenRepository/org/jetbrains/kotlin/kotlin-stdlib/1.3.71/kotlin-stdlib-1.3.71.jar, file:/D:/MavenRepository/org/jetbrains/kotlin/kotlin-stdlib-common/1.3.71/kotlin-stdlib-common-1.3.71.jar, file:/D:/MavenRepository/org/jetbrains/annotations/13.0/annotations-13.0.jar, file:/D:/MavenRepository/org/jetbrains/kotlin/kotlin-stdlib-jdk8/1.3.71/kotlin-stdlib-jdk8-1.3.71.jar, file:/D:/MavenRepository/org/jetbrains/kotlin/kotlin-stdlib-jdk7/1.3.71/kotlin-stdlib-jdk7-1.3.71.jar, file:/D:/MavenRepository/com/squareup/okhttp3/logging-interceptor/4.11.0/logging-interceptor-4.11.0.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter-aop/2.2.6.RELEASE/spring-boot-starter-aop-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/springframework/spring-aop/5.2.5.RELEASE/spring-aop-5.2.5.RELEASE.jar, file:/D:/MavenRepository/org/aspectj/aspectjweaver/1.9.5/aspectjweaver-1.9.5.jar, file:/D:/MavenRepository/org/mybatis/spring/boot/mybatis-spring-boot-starter/2.2.0/mybatis-spring-boot-starter-2.2.0.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter-jdbc/2.2.6.RELEASE/spring-boot-starter-jdbc-2.2.6.RELEASE.jar, file:/D:/MavenRepository/com/zaxxer/HikariCP/3.4.2/HikariCP-3.4.2.jar, file:/D:/MavenRepository/org/springframework/spring-jdbc/5.2.5.RELEASE/spring-jdbc-5.2.5.RELEASE.jar, file:/D:/MavenRepository/org/mybatis/spring/boot/mybatis-spring-boot-autoconfigure/2.2.0/mybatis-spring-boot-autoconfigure-2.2.0.jar, file:/D:/MavenRepository/org/mybatis/mybatis/3.5.7/mybatis-3.5.7.jar, file:/D:/MavenRepository/org/mybatis/mybatis-spring/2.0.6/mybatis-spring-2.0.6.jar, file:/D:/MavenRepository/net/minidev/json-smart/2.3/json-smart-2.3.jar, file:/D:/MavenRepository/net/minidev/accessors-smart/1.2/accessors-smart-1.2.jar, file:/D:/MavenRepository/org/ow2/asm/asm/5.0.4/asm-5.0.4.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter/2.2.6.RELEASE/spring-boot-starter-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot/2.2.6.RELEASE/spring-boot-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/springframework/spring-context/5.2.5.RELEASE/spring-context-5.2.5.RELEASE.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter-logging/2.2.6.RELEASE/spring-boot-starter-logging-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/apache/logging/log4j/log4j-to-slf4j/2.12.1/log4j-to-slf4j-2.12.1.jar, file:/D:/MavenRepository/org/apache/logging/log4j/log4j-api/2.12.1/log4j-api-2.12.1.jar, file:/D:/MavenRepository/org/slf4j/jul-to-slf4j/1.7.30/jul-to-slf4j-1.7.30.jar, file:/D:/MavenRepository/jakarta/annotation/jakarta.annotation-api/1.3.5/jakarta.annotation-api-1.3.5.jar, file:/D:/MavenRepository/org/yaml/snakeyaml/1.25/snakeyaml-1.25.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-test/2.2.6.RELEASE/spring-boot-test-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/slf4j/slf4j-api/1.7.30/slf4j-api-1.7.30.jar, file:/D:/MavenRepository/org/springframework/spring-core/5.2.5.RELEASE/spring-core-5.2.5.RELEASE.jar, file:/D:/MavenRepository/org/springframework/spring-jcl/5.2.5.RELEASE/spring-jcl-5.2.5.RELEASE.jar, file:/D:/MavenRepository/org/springframework/spring-test/5.2.5.RELEASE/spring-test-5.2.5.RELEASE.jar, file:/D:/MavenRepository/net/bytebuddy/byte-buddy/1.10.8/byte-buddy-1.10.8.jar, file:/D:/Idea/IntelliJ%20IDEA%202025.1.4.1/lib/idea_rt.jar, file:/C:/Users/<USER>/AppData/Local/JetBrains/IntelliJIdea2025.1/captureAgent/debugger-agent.jar]","logger_name":"org.springframework.boot.context.logging.ClasspathLoggingApplicationListener","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-07T09:19:17.297+08:00","@version":"1","message":"Starting GencoAdminApplication on LAPTOP-3I7P6FTL with PID 19104 (C:\\Users\\<USER>\\Desktop\\shop\\api\\genco-admin\\target\\classes started by 吴兴龙 in C:\\Users\\<USER>\\Desktop\\shop)","logger_name":"com.genco.admin.GencoAdminApplication","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:17.300+08:00","@version":"1","message":"The following profiles are active: prod","logger_name":"com.genco.admin.GencoAdminApplication","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:17.302+08:00","@version":"1","message":"Loading source class com.genco.admin.GencoAdminApplication","logger_name":"org.springframework.boot.SpringApplication","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-07T09:19:17.393+08:00","@version":"1","message":"Activated activeProfiles prod","logger_name":"org.springframework.boot.context.config.ConfigFileApplicationListener","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-07T09:19:17.393+08:00","@version":"1","message":"Loaded config file 'file:/C:/Users/<USER>/Desktop/shop/api/genco-admin/target/classes/application.yml' (classpath:/application.yml)","logger_name":"org.springframework.boot.context.config.ConfigFileApplicationListener","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-07T09:19:17.393+08:00","@version":"1","message":"Profiles already activated, '[prod]' will not be applied","logger_name":"org.springframework.boot.context.config.ConfigFileApplicationListener","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-07T09:19:17.393+08:00","@version":"1","message":"Loaded config file 'file:/C:/Users/<USER>/Desktop/shop/api/genco-admin/target/classes/application-prod.yml' (classpath:/application-prod.yml) for profile prod","logger_name":"org.springframework.boot.context.config.ConfigFileApplicationListener","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-07T09:19:17.394+08:00","@version":"1","message":"Refreshing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@3e2822","logger_name":"org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-07T09:19:19.176+08:00","@version":"1","message":"Multiple Spring Data modules found, entering strict repository configuration mode!","logger_name":"org.springframework.data.repository.config.RepositoryConfigurationDelegate","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:19.180+08:00","@version":"1","message":"Bootstrapping Spring Data Redis repositories in DEFAULT mode.","logger_name":"org.springframework.data.repository.config.RepositoryConfigurationDelegate","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:19.240+08:00","@version":"1","message":"Finished Spring Data repository scanning in 37ms. Found 0 Redis repository interfaces.","logger_name":"org.springframework.data.repository.config.RepositoryConfigurationDelegate","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:20.171+08:00","@version":"1","message":"Bean 'org.springframework.security.config.annotation.configuration.ObjectPostProcessorConfiguration' of type [org.springframework.security.config.annotation.configuration.ObjectPostProcessorConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)","logger_name":"org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:20.178+08:00","@version":"1","message":"Bean 'objectPostProcessor' of type [org.springframework.security.config.annotation.configuration.AutowireBeanFactoryObjectPostProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)","logger_name":"org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:20.183+08:00","@version":"1","message":"Bean 'org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler@4a9b3956' of type [org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)","logger_name":"org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:20.185+08:00","@version":"1","message":"Bean 'org.springframework.security.config.annotation.method.configuration.GlobalMethodSecurityConfiguration' of type [org.springframework.security.config.annotation.method.configuration.GlobalMethodSecurityConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)","logger_name":"org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:20.194+08:00","@version":"1","message":"Bean 'methodSecurityMetadataSource' of type [org.springframework.security.access.method.DelegatingMethodSecurityMetadataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)","logger_name":"org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:20.532+08:00","@version":"1","message":"Code archive: D:\\MavenRepository\\org\\springframework\\boot\\spring-boot\\2.2.6.RELEASE\\spring-boot-2.2.6.RELEASE.jar","logger_name":"org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-07T09:19:20.533+08:00","@version":"1","message":"Code archive: D:\\MavenRepository\\org\\springframework\\boot\\spring-boot\\2.2.6.RELEASE\\spring-boot-2.2.6.RELEASE.jar","logger_name":"org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-07T09:19:20.533+08:00","@version":"1","message":"None of the document roots [src/main/webapp, public, static] point to a directory and will be ignored.","logger_name":"org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-07T09:19:20.580+08:00","@version":"1","message":"Tomcat initialized with port(s): 20000 (http)","logger_name":"org.springframework.boot.web.embedded.tomcat.TomcatWebServer","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:20.592+08:00","@version":"1","message":"Initializing ProtocolHandler [\"http-nio-20000\"]","logger_name":"org.apache.coyote.http11.Http11NioProtocol","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:20.593+08:00","@version":"1","message":"Starting service [Tomcat]","logger_name":"org.apache.catalina.core.StandardService","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:20.593+08:00","@version":"1","message":"Starting Servlet engine: [Apache Tomcat/9.0.33]","logger_name":"org.apache.catalina.core.StandardEngine","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:20.868+08:00","@version":"1","message":"At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.","logger_name":"org.apache.jasper.servlet.TldScanner","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:20.878+08:00","@version":"1","message":"Initializing Spring embedded WebApplicationContext","logger_name":"org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:20.878+08:00","@version":"1","message":"Published root WebApplicationContext as ServletContext attribute with name [org.springframework.web.context.WebApplicationContext.ROOT]","logger_name":"org.springframework.web.context.ContextLoader","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-07T09:19:20.878+08:00","@version":"1","message":"Root WebApplicationContext: initialization completed in 3484 ms","logger_name":"org.springframework.web.context.ContextLoader","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:21.585+08:00","@version":"1","message":"Mapping filters: filterRegistrationBean urls=[/*] order=-2147483647, springSecurityFilterChain urls=[/*] order=-100, filterRegistrationBean urls=[/*] order=2147483647, filterRegistrationBean urls=[/api/admin/*, /api/front/*] order=2147483647, characterEncodingFilter urls=[/*] order=-2147483648, formContentFilter urls=[/*] order=-9900, requestContextFilter urls=[/*] order=-105, corsFilter urls=[/*] order=2147483647, jwtAuthenticationTokenFilter urls=[/*] order=2147483647","logger_name":"org.springframework.boot.web.servlet.ServletContextInitializerBeans","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-07T09:19:21.586+08:00","@version":"1","message":"Mapping servlets: statViewServlet urls=[/druid/*], dispatcherServlet urls=[/]","logger_name":"org.springframework.boot.web.servlet.ServletContextInitializerBeans","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-07T09:19:21.620+08:00","@version":"1","message":"Filter 'webMvcMetricsFilter' configured for use","logger_name":"org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-07T09:19:21.620+08:00","@version":"1","message":"Filter 'requestContextFilter' configured for use","logger_name":"org.springframework.boot.web.servlet.filter.OrderedRequestContextFilter","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-07T09:19:21.620+08:00","@version":"1","message":"Filter 'corsFilter' configured for use","logger_name":"org.springframework.web.filter.CorsFilter","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-07T09:19:21.621+08:00","@version":"1","message":"Filter 'characterEncodingFilter' configured for use","logger_name":"org.springframework.boot.web.servlet.filter.OrderedCharacterEncodingFilter","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-07T09:19:21.621+08:00","@version":"1","message":"Filter 'springSecurityFilterChain' configured for use","logger_name":"org.springframework.boot.web.servlet.DelegatingFilterProxyRegistrationBean$1","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-07T09:19:21.621+08:00","@version":"1","message":"Filter 'formContentFilter' configured for use","logger_name":"org.springframework.boot.web.servlet.filter.OrderedFormContentFilter","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-07T09:19:22.332+08:00","@version":"1","message":"Warn: Could not find @TableId in Class: com.genco.common.vo.UserFundsMonitor.","logger_name":"com.baomidou.mybatisplus.core.metadata.TableInfoHelper","thread_name":"main","level":"WARN","level_value":30000,"tags":["MYBATIS"]}
{"@timestamp":"2025-08-07T09:19:22.581+08:00","@version":"1","message":"Warn: Could not find @TableId in Class: com.genco.common.model.order.StoreOrderStatus.","logger_name":"com.baomidou.mybatisplus.core.metadata.TableInfoHelper","thread_name":"main","level":"WARN","level_value":30000,"tags":["MYBATIS"]}
{"@timestamp":"2025-08-07T09:19:22.877+08:00","@version":"1","message":"Warn: Could not find @TableId in Class: com.genco.common.model.system.SystemRoleMenu.","logger_name":"com.baomidou.mybatisplus.core.metadata.TableInfoHelper","thread_name":"main","level":"WARN","level_value":30000,"tags":["MYBATIS"]}
{"@timestamp":"2025-08-07T09:19:27.710+08:00","@version":"1","message":"支付策略初始化完成，支持的支付类型: [offline, alipay, weixin, xendit, yue, haipay]","logger_name":"com.genco.service.service.impl.PaymentStrategyFactory","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:29.607+08:00","@version":"1","message":"298 mappings in 'requestMappingHandlerMapping'","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-07T09:19:29.717+08:00","@version":"1","message":"Exposing 2 endpoint(s) beneath base path '/actuator'","logger_name":"org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:29.909+08:00","@version":"1","message":"Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]","logger_name":"springfox.documentation.spring.web.PropertySourcedRequestMappingHandlerMapping","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:29.991+08:00","@version":"1","message":"Initializing ExecutorService","logger_name":"org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:29.993+08:00","@version":"1","message":"Initializing ExecutorService 'taskExecutor'","logger_name":"org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:30.313+08:00","@version":"1","message":"Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@363e2009, org.springframework.security.web.context.SecurityContextPersistenceFilter@13f17b8f, org.springframework.security.web.header.HeaderWriterFilter@1c76b2fe, org.springframework.web.filter.CorsFilter@6528d339, org.springframework.web.filter.CorsFilter@6528d339, org.springframework.web.filter.CorsFilter@6528d339, org.springframework.security.web.authentication.logout.LogoutFilter@3bdc8975, com.genco.admin.filter.JwtAuthenticationTokenFilter@4b99648a, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@11a29c0, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@212be8c, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@7b55fc83, org.springframework.security.web.session.SessionManagementFilter@6e5a77ef, org.springframework.security.web.access.ExceptionTranslationFilter@5abc488d, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@141c66db]","logger_name":"org.springframework.security.web.DefaultSecurityFilterChain","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:30.646+08:00","@version":"1","message":"Application Admin MBean registered with name 'org.springframework.boot:type=Admin,name=SpringApplication'","logger_name":"org.springframework.boot.admin.SpringApplicationAdminMXBeanRegistrar$SpringApplicationAdmin","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-07T09:19:30.668+08:00","@version":"1","message":"ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-07T09:19:30.823+08:00","@version":"1","message":"Patterns [/webjars/**, /**, /doc.html, /crmebimage/**] in 'resourceHandlerMapping'","logger_name":"org.springframework.web.servlet.handler.SimpleUrlHandlerMapping","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-07T09:19:30.860+08:00","@version":"1","message":"ControllerAdvice beans: 1 @ExceptionHandler, 1 ResponseBodyAdvice","logger_name":"org.springframework.web.servlet.mvc.method.annotation.ExceptionHandlerExceptionResolver","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-07T09:19:32.265+08:00","@version":"1","message":"Context refreshed","logger_name":"springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:32.355+08:00","@version":"1","message":"Found 2 custom documentation plugin(s)","logger_name":"springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:32.587+08:00","@version":"1","message":"Scanning for api listing references","logger_name":"springfox.documentation.spring.web.scanners.ApiListingReferenceScanner","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:33.293+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:33.306+08:00","@version":"1","message":"Generating unique operation named: getByIdsUsingGET_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:33.316+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:33.330+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:33.346+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:33.347+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:33.389+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_2","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:33.392+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_2","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:33.431+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_2","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:33.437+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_3","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:33.462+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_3","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:33.508+08:00","@version":"1","message":"Generating unique operation named: updatePhoneUsingPOST_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:33.525+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_4","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:33.594+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_2","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:33.605+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_5","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:33.607+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_3","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:33.616+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_2","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:33.618+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_4","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:33.622+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_6","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:33.625+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_7","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:33.632+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_3","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:33.681+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_8","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:33.717+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_4","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:33.741+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_3","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:33.742+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_5","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:33.786+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_4","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:33.821+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_9","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:33.830+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_5","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:33.843+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_4","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:33.845+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_6","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:33.847+08:00","@version":"1","message":"Generating unique operation named: updateStatusUsingPOST_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:33.883+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_10","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:33.895+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_5","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:33.896+08:00","@version":"1","message":"Generating unique operation named: updateStatusUsingPOST_2","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:33.909+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_11","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:33.918+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_5","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:33.961+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_12","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.030+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_6","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.069+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_13","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.092+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_6","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.154+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_14","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.175+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_7","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.189+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_6","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.191+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_7","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.196+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_7","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.212+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_15","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.215+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_8","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.219+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_7","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.221+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_8","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.228+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_16","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.229+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_9","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.231+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_8","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.232+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_8","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.238+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_17","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.239+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_9","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.264+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_18","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.266+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_10","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.274+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_9","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.275+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_9","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.277+08:00","@version":"1","message":"Generating unique operation named: updateStatusUsingPOST_3","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.278+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_10","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.286+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_19","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.289+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_11","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.293+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_10","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.294+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_10","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.295+08:00","@version":"1","message":"Generating unique operation named: updateStatusUsingPOST_4","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.295+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_11","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.303+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_20","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.309+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_12","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.315+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_11","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.320+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_11","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.325+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_12","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.330+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_21","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.333+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_13","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.336+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_12","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.337+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_12","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.347+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_22","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.354+08:00","@version":"1","message":"Generating unique operation named: getListTreeUsingGET_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.357+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_14","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.364+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_13","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.365+08:00","@version":"1","message":"Generating unique operation named: updateStatusUsingPOST_5","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.379+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_15","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.395+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_23","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.397+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_16","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.400+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_13","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.401+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_14","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.403+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_13","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.410+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_24","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.413+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_17","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.420+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_14","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.423+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_15","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.424+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_14","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.434+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_25","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.436+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_18","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.440+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_15","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.442+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_16","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.449+08:00","@version":"1","message":"Generating unique operation named: deleteUsingPOST_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.460+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_26","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.461+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_19","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.461+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_17","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.470+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_27","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.483+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_20","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.491+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_18","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.494+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_15","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.506+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_28","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.512+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_21","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.515+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_16","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.516+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_19","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.517+08:00","@version":"1","message":"Generating unique operation named: updateStatusUsingGET_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.518+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_16","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.525+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_29","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.526+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_22","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.534+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_17","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.535+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_20","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.536+08:00","@version":"1","message":"Generating unique operation named: updateStatusUsingGET_2","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.540+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_17","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.550+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_30","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.553+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_23","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.559+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_18","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.559+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_21","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.560+08:00","@version":"1","message":"Generating unique operation named: updateStatusUsingGET_3","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.561+08:00","@version":"1","message":"Generating unique operation named: deleteUsingPOST_2","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.563+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_31","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.571+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_19","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.572+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_22","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.679+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_32","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.682+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_24","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.709+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_23","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.734+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_33","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.742+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_24","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.744+08:00","@version":"1","message":"Generating unique operation named: updateStatusUsingPOST_6","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.746+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_18","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.751+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_34","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.753+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_25","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.758+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_20","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.760+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_25","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.773+08:00","@version":"1","message":"Generating unique operation named: getListUsingPOST_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.776+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_35","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.777+08:00","@version":"1","message":"Generating unique operation named: balanceUsingPOST_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.794+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_36","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.797+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_19","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.800+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_37","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.802+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_26","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.804+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_21","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.805+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_26","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.852+08:00","@version":"1","message":"Scanning for api listing references","logger_name":"springfox.documentation.spring.web.scanners.ApiListingReferenceScanner","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.881+08:00","@version":"1","message":"Initializing ExecutorService","logger_name":"org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.902+08:00","@version":"1","message":"---TiktokTokenRefreshTask task------produce Data with fixed rate task: Execution Time - Thu Aug 07 09:19:34 CST 2025","logger_name":"com.genco.admin.task.tiktok.TiktokTokenRefreshTask","thread_name":"crmeb-scheduled-task-pool-1","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:34.908+08:00","@version":"1","message":"Starting ProtocolHandler [\"http-nio-20000\"]","logger_name":"org.apache.coyote.http11.Http11NioProtocol","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:35.343+08:00","@version":"1","message":"Tomcat started on port(s): 20000 (http) with context path ''","logger_name":"org.springframework.boot.web.embedded.tomcat.TomcatWebServer","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:35.357+08:00","@version":"1","message":"Started GencoAdminApplication in 18.903 seconds (JVM running for 20.011)","logger_name":"com.genco.admin.GencoAdminApplication","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:35.666+08:00","@version":"1","message":"{dataSource-1} inited","logger_name":"com.alibaba.druid.pool.DruidDataSource","thread_name":"crmeb-scheduled-task-pool-2","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:36.512+08:00","@version":"1","message":"Initializing Spring DispatcherServlet 'dispatcherServlet'","logger_name":"org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]","thread_name":"RMI TCP Connection(8)-************","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:36.513+08:00","@version":"1","message":"Initializing Servlet 'dispatcherServlet'","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"RMI TCP Connection(8)-************","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:36.516+08:00","@version":"1","message":"Detected StandardServletMultipartResolver","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"RMI TCP Connection(8)-************","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-07T09:19:36.546+08:00","@version":"1","message":"enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"RMI TCP Connection(8)-************","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-07T09:19:36.546+08:00","@version":"1","message":"Completed initialization in 33 ms","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"RMI TCP Connection(8)-************","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:38.767+08:00","@version":"1","message":"Tiktok access_token 未到刷新时间，当前时间: 1754529574，过期时间: 1781838424","logger_name":"com.genco.admin.task.tiktok.TiktokTokenRefreshTask","thread_name":"crmeb-scheduled-task-pool-1","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:38.997+08:00","@version":"1","message":"Executing SQL query [/* ping */ SELECT 1]","logger_name":"org.springframework.jdbc.core.JdbcTemplate","thread_name":"RMI TCP Connection(5)-************","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-07T09:19:39.977+08:00","@version":"1","message":"开始同步TikTok订单 - pageToken: null, startTime: Mon Aug 04 09:19:19 CST 2025, endTime: Thu Aug 07 09:19:19 CST 2025","logger_name":"com.genco.service.service.impl.TiktokOrderSyncServiceImpl","thread_name":"crmeb-scheduled-task-pool-2","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:44.768+08:00","@version":"1","message":"开始调用TikTok API获取订单数据","logger_name":"com.genco.service.service.impl.TiktokOrderSyncServiceImpl","thread_name":"crmeb-scheduled-task-pool-2","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:47.172+08:00","@version":"1","message":"API调用成功，获取到下一页token: , 开始处理订单数据","logger_name":"com.genco.service.service.impl.TiktokOrderSyncServiceImpl","thread_name":"crmeb-scheduled-task-pool-2","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:47.425+08:00","@version":"1","message":"开始处理 3 个订单","logger_name":"com.genco.service.service.impl.TiktokOrderSyncServiceImpl","thread_name":"crmeb-scheduled-task-pool-2","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:48.217+08:00","@version":"1","message":"订单批量处理完成 - 总计: 3, 新增: 0, 更新: 0, 跳过: 3, 错误: 0","logger_name":"com.genco.service.service.impl.TiktokOrderSyncServiceImpl","thread_name":"crmeb-scheduled-task-pool-2","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:48.677+08:00","@version":"1","message":"TikTok订单同步事务执行完成，下一页token: ","logger_name":"com.genco.service.service.impl.TiktokOrderSyncServiceImpl","thread_name":"crmeb-scheduled-task-pool-2","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:19:48.677+08:00","@version":"1","message":"TikTok订单同步方法执行完成，返回下一页token: ","logger_name":"com.genco.service.service.impl.TiktokOrderSyncServiceImpl","thread_name":"crmeb-scheduled-task-pool-2","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:20:50.338+08:00","@version":"1","message":"创建TikTok订单拉取初始任务，时间范围：Mon Aug 04 09:20:49 CST 2025 至 Thu Aug 07 09:20:49 CST 2025","logger_name":"com.genco.admin.task.order.OrderTiktokPullTask","thread_name":"crmeb-scheduled-task-pool-1","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:21:51.513+08:00","@version":"1","message":"开始同步TikTok订单 - pageToken: null, startTime: Mon Aug 04 09:21:20 CST 2025, endTime: Thu Aug 07 09:21:20 CST 2025","logger_name":"com.genco.service.service.impl.TiktokOrderSyncServiceImpl","thread_name":"crmeb-scheduled-task-pool-7","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:21:52.199+08:00","@version":"1","message":"开始调用TikTok API获取订单数据","logger_name":"com.genco.service.service.impl.TiktokOrderSyncServiceImpl","thread_name":"crmeb-scheduled-task-pool-7","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:21:52.570+08:00","@version":"1","message":"API调用成功，获取到下一页token: , 开始处理订单数据","logger_name":"com.genco.service.service.impl.TiktokOrderSyncServiceImpl","thread_name":"crmeb-scheduled-task-pool-7","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:21:52.796+08:00","@version":"1","message":"开始处理 3 个订单","logger_name":"com.genco.service.service.impl.TiktokOrderSyncServiceImpl","thread_name":"crmeb-scheduled-task-pool-7","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:21:53.555+08:00","@version":"1","message":"订单批量处理完成 - 总计: 3, 新增: 0, 更新: 0, 跳过: 3, 错误: 0","logger_name":"com.genco.service.service.impl.TiktokOrderSyncServiceImpl","thread_name":"crmeb-scheduled-task-pool-7","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:21:54.059+08:00","@version":"1","message":"TikTok订单同步事务执行完成，下一页token: ","logger_name":"com.genco.service.service.impl.TiktokOrderSyncServiceImpl","thread_name":"crmeb-scheduled-task-pool-7","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:21:54.059+08:00","@version":"1","message":"TikTok订单同步方法执行完成，返回下一页token: ","logger_name":"com.genco.service.service.impl.TiktokOrderSyncServiceImpl","thread_name":"crmeb-scheduled-task-pool-7","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:22:55.693+08:00","@version":"1","message":"创建TikTok订单拉取初始任务，时间范围：Mon Aug 04 09:22:55 CST 2025 至 Thu Aug 07 09:22:55 CST 2025","logger_name":"com.genco.admin.task.order.OrderTiktokPullTask","thread_name":"crmeb-scheduled-task-pool-3","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:22:57.773+08:00","@version":"1","message":"Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception","logger_name":"org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/].[dispatcherServlet]","thread_name":"http-nio-20000-exec-8","level":"ERROR","level_value":40000,"stack_trace":"org.springframework.security.web.firewall.RequestRejectedException: The request was rejected because the URL contained a potentially malicious String \"//\"\r\n\tat org.springframework.security.web.firewall.StrictHttpFirewall.rejectedBlacklistedUrls(StrictHttpFirewall.java:369)\r\n\tat org.springframework.security.web.firewall.StrictHttpFirewall.getFirewalledRequest(StrictHttpFirewall.java:336)\r\n\tat org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:194)\r\n\tat org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)\r\n\tat org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)\r\n\tat org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)\r\n\tat org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)\r\n\tat org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)\r\n\tat org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)\r\n\tat org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)\r\n\tat org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)\r\n\tat org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)\r\n\tat org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)\r\n\tat org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)\r\n\tat org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)\r\n\tat org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)\r\n\tat org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:109)\r\n\tat org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)\r\n\tat org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)\r\n\tat org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)\r\n\tat org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)\r\n\tat org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)\r\n\tat org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)\r\n\tat org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)\r\n\tat org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)\r\n\tat org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)\r\n\tat org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)\r\n\tat org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)\r\n\tat org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)\r\n\tat org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)\r\n\tat org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)\r\n\tat org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:373)\r\n\tat org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)\r\n\tat org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)\r\n\tat org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1594)\r\n\tat org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)\r\n\tat java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)\r\n\tat java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)\r\n\tat org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)\r\n\tat java.lang.Thread.run(Thread.java:750)\r\n"}
{"@timestamp":"2025-08-07T09:22:57.931+08:00","@version":"1","message":"GET \"/api/admin/getLoginPic?temp=**********\", parameters={masked}","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-20000-exec-19","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-07T09:22:57.932+08:00","@version":"1","message":"GET \"/api/admin/validate/code/get?temp=**********\", parameters={masked}","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-20000-exec-6","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-07T09:22:58.001+08:00","@version":"1","message":"Mapped to com.genco.admin.controller.AdminLoginController#getLoginPic()","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping","thread_name":"http-nio-20000-exec-19","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-07T09:22:58.001+08:00","@version":"1","message":"Mapped to com.genco.admin.controller.ValidateCodeController#get()","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping","thread_name":"http-nio-20000-exec-6","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-07T09:22:58.006+08:00","@version":"1","message":"[DIGEST] 请求开始 - TraceId: 1399955699384b32bd5617b0190980b4, 类名: ValidateCodeController, 方法名: get, 用户ID: null, URI: /api/admin/validate/code/get, 方法: GET","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20000-exec-6","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:22:58.006+08:00","@version":"1","message":"[DIGEST] 请求开始 - TraceId: 1aca5917c88b49868cd11cc42be5c076, 类名: AdminLoginController, 方法名: getLoginPic, 用户ID: null, URI: /api/admin/getLoginPic, 方法: GET","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20000-exec-19","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:22:58.189+08:00","@version":"1","message":"[DETAIL] 请求详情: {\"traceId\":\"1aca5917c88b49868cd11cc42be5c076\",\"method\":\"GET\",\"requestBody\":\"未记录\",\"clientIp\":\"127.0.0.1\",\"methodName\":\"getLoginPic\",\"requestParams\":{\"request\":{\"temp\":\"**********\"},\"method\":\"GET\",\"request_filter\":{},\"header\":{\"sec-fetch-mode\":\"cors\",\"referer\":\"http://localhost:9527/\",\"sec-fetch-site\":\"cross-site\",\"accept-language\":\"zh-CN,zh;q=0.9\",\"origin\":\"http://localhost:9527\",\"accept\":\"application/json, text/plain, */*\",\"sec-ch-ua\":\"\\\"Chromium\\\";v=\\\"9\\\", \\\"Not?A_Brand\\\";v=\\\"8\\\"\",\"sec-ch-ua-mobile\":\"?0\",\"sec-ch-ua-platform\":\"\\\"Windows\\\"\",\"host\":\"127.0.0.1:20000\",\"connection\":\"keep-alive\",\"accept-encoding\":\"gzip, deflate, br, zstd\",\"user-agent\":\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit\",\"sec-fetch-dest\":\"empty\"},\"uri\":\"/api/admin/getLoginPic\",\"url\":\"http://127.0.0.1:20000/api/admin/getLoginPic\"},\"className\":\"com.genco.admin.controller.AdminLoginController\",\"userAgent\":\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit\",\"uri\":\"/api/admin/getLoginPic\"}","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20000-exec-19","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:22:58.189+08:00","@version":"1","message":"[DETAIL] 请求详情: {\"traceId\":\"1399955699384b32bd5617b0190980b4\",\"method\":\"GET\",\"requestBody\":\"未记录\",\"clientIp\":\"127.0.0.1\",\"methodName\":\"get\",\"requestParams\":{\"request\":{\"temp\":\"**********\"},\"method\":\"GET\",\"request_filter\":{},\"header\":{\"sec-fetch-mode\":\"cors\",\"referer\":\"http://localhost:9527/\",\"sec-fetch-site\":\"cross-site\",\"accept-language\":\"zh-CN,zh;q=0.9\",\"origin\":\"http://localhost:9527\",\"accept\":\"application/json, text/plain, */*\",\"sec-ch-ua\":\"\\\"Chromium\\\";v=\\\"9\\\", \\\"Not?A_Brand\\\";v=\\\"8\\\"\",\"sec-ch-ua-mobile\":\"?0\",\"sec-ch-ua-platform\":\"\\\"Windows\\\"\",\"host\":\"127.0.0.1:20000\",\"connection\":\"keep-alive\",\"accept-encoding\":\"gzip, deflate, br, zstd\",\"user-agent\":\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit\",\"sec-fetch-dest\":\"empty\"},\"uri\":\"/api/admin/validate/code/get\",\"url\":\"http://127.0.0.1:20000/api/admin/validate/code/get\"},\"className\":\"com.genco.admin.controller.ValidateCodeController\",\"userAgent\":\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit\",\"uri\":\"/api/admin/validate/code/get\"}","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20000-exec-6","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:22:58.990+08:00","@version":"1","message":"Controller method：CommonResult com.genco.admin.controller.ValidateCodeController.get()，prams：[]，cost time：660542700 ns，cost：660 ms","logger_name":"com.genco.admin.acpect.ControllerAspect","thread_name":"http-nio-20000-exec-6","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:22:59.007+08:00","@version":"1","message":"Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-20000-exec-6","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-07T09:22:59.008+08:00","@version":"1","message":"Writing [com.genco.common.response.CommonResult@56f1e1f9]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-20000-exec-6","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-07T09:22:59.065+08:00","@version":"1","message":"[DIGEST] 请求结束 - TraceId: 1399955699384b32bd5617b0190980b4, 类名: ValidateCodeController, 方法名: get, 用户ID: null, 是否成功: true, 响应时间: 1059ms, 状态码: 200","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20000-exec-6","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:22:59.065+08:00","@version":"1","message":"[DETAIL] 响应详情: {\"traceId\":\"1399955699384b32bd5617b0190980b4\",\"requestBody\":\"未记录\",\"responseTime\":1059,\"methodName\":\"get\",\"className\":\"com.genco.admin.controller.ValidateCodeController\",\"isSuccess\":true,\"statusCode\":200}","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20000-exec-6","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:22:59.066+08:00","@version":"1","message":"Completed 200 OK","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-20000-exec-6","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-07T09:23:00.008+08:00","@version":"1","message":"Controller method：CommonResult com.genco.admin.controller.AdminLoginController.getLoginPic()，prams：[]，cost time：********** ns，cost：1689 ms","logger_name":"com.genco.admin.acpect.ControllerAspect","thread_name":"http-nio-20000-exec-19","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:23:00.009+08:00","@version":"1","message":"Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-20000-exec-19","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-07T09:23:00.011+08:00","@version":"1","message":"Writing [com.genco.common.response.CommonResult@5559d64a]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-20000-exec-19","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-07T09:23:00.013+08:00","@version":"1","message":"[DIGEST] 请求结束 - TraceId: 1aca5917c88b49868cd11cc42be5c076, 类名: AdminLoginController, 方法名: getLoginPic, 用户ID: null, 是否成功: true, 响应时间: 2007ms, 状态码: 200","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20000-exec-19","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:23:00.013+08:00","@version":"1","message":"[DETAIL] 响应详情: {\"traceId\":\"1aca5917c88b49868cd11cc42be5c076\",\"requestBody\":\"未记录\",\"responseTime\":2007,\"methodName\":\"getLoginPic\",\"className\":\"com.genco.admin.controller.AdminLoginController\",\"isSuccess\":true,\"statusCode\":200}","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20000-exec-19","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:23:00.013+08:00","@version":"1","message":"Completed 200 OK","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-20000-exec-19","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-07T09:23:05.610+08:00","@version":"1","message":"POST \"/api/admin/login\", parameters={}","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-20000-exec-14","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-07T09:23:05.611+08:00","@version":"1","message":"Mapped to com.genco.admin.controller.AdminLoginController#SystemAdminLogin(SystemAdminLoginRequest, HttpServletRequest)","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping","thread_name":"http-nio-20000-exec-14","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-07T09:23:05.611+08:00","@version":"1","message":"[DIGEST] 请求开始 - TraceId: 51c055de0f564957905915b6b3fd7295, 类名: AdminLoginController, 方法名: SystemAdminLogin, 用户ID: null, URI: /api/admin/login, 方法: POST","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20000-exec-14","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:23:05.611+08:00","@version":"1","message":"[DETAIL] 请求详情: {\"traceId\":\"51c055de0f564957905915b6b3fd7295\",\"method\":\"POST\",\"requestBody\":\"未记录\",\"clientIp\":\"127.0.0.1\",\"methodName\":\"SystemAdminLogin\",\"requestParams\":{\"request\":{},\"method\":\"POST\",\"request_filter\":{},\"header\":{\"sec-fetch-mode\":\"cors\",\"content-length\":\"107\",\"referer\":\"http://localhost:9527/\",\"sec-fetch-site\":\"cross-site\",\"accept-language\":\"zh-CN,zh;q=0.9\",\"origin\":\"http://localhost:9527\",\"accept\":\"application/json, text/plain, */*\",\"sec-ch-ua\":\"\\\"Chromium\\\";v=\\\"9\\\", \\\"Not?A_Brand\\\";v=\\\"8\\\"\",\"sec-ch-ua-mobile\":\"?0\",\"sec-ch-ua-platform\":\"\\\"Windows\\\"\",\"host\":\"127.0.0.1:20000\",\"connection\":\"keep-alive\",\"content-type\":\"application/json\",\"accept-encoding\":\"gzip, deflate, br, zstd\",\"user-agent\":\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit\",\"sec-fetch-dest\":\"empty\"},\"uri\":\"/api/admin/login\",\"url\":\"http://127.0.0.1:20000/api/admin/login\"},\"className\":\"com.genco.admin.controller.AdminLoginController\",\"userAgent\":\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit\",\"uri\":\"/api/admin/login\"}","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20000-exec-14","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:23:05.773+08:00","@version":"1","message":"Read \"application/json;charset=UTF-8\" to [SystemAdminLoginRequest(account=admin, pwd=BzX36YjvNR8o, key=3e0c35242e766e78e3660855a2ad46c2, code= (truncated)...]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-20000-exec-14","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-07T09:23:08.228+08:00","@version":"1","message":"Controller method：CommonResult com.genco.admin.controller.AdminLoginController.SystemAdminLogin(SystemAdminLoginRequest,HttpServletRequest)，prams：[SystemAdminLoginRequest(account=admin, pwd=BzX36YjvNR8o, key=3e0c35242e766e78e3660855a2ad46c2, code=djuh), SecurityContextHolderAwareRequestWrapper[ org.springframework.security.web.header.HeaderWriterFilter$HeaderWriterRequest@24f585e]]，cost time：********** ns，cost：2091 ms","logger_name":"com.genco.admin.acpect.ControllerAspect","thread_name":"http-nio-20000-exec-14","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:23:08.228+08:00","@version":"1","message":"Using 'application/json', given [application/json, text/plain, */*] and supported [application/json]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-20000-exec-14","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-07T09:23:08.228+08:00","@version":"1","message":"Writing [com.genco.common.response.CommonResult@7fbfb4fc]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-20000-exec-14","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-07T09:23:08.228+08:00","@version":"1","message":"[DIGEST] 请求结束 - TraceId: 51c055de0f564957905915b6b3fd7295, 类名: AdminLoginController, 方法名: SystemAdminLogin, 用户ID: null, 是否成功: true, 响应时间: 2617ms, 状态码: 200","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20000-exec-14","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:23:08.228+08:00","@version":"1","message":"[DETAIL] 响应详情: {\"traceId\":\"51c055de0f564957905915b6b3fd7295\",\"requestBody\":\"未记录\",\"responseTime\":2617,\"methodName\":\"SystemAdminLogin\",\"className\":\"com.genco.admin.controller.AdminLoginController\",\"isSuccess\":true,\"statusCode\":200}","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20000-exec-14","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:23:08.228+08:00","@version":"1","message":"Completed 200 OK","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-20000-exec-14","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-07T09:23:08.311+08:00","@version":"1","message":"GET \"/api/admin/system/store/staff/list?page=1&limit=9999&temp=1754529788\", parameters={masked}","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-20000-exec-30","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-07T09:23:08.311+08:00","@version":"1","message":"GET \"/api/admin/getAdminInfoByToken?token=f314572531e74f07ae6e19bc2c87d804&temp=1754529788\", parameters={masked}","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-20000-exec-7","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-07T09:23:08.311+08:00","@version":"1","message":"Mapped to com.genco.admin.controller.SystemStoreStaffController#getList(Integer, PageParamRequest)","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping","thread_name":"http-nio-20000-exec-30","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-07T09:23:08.311+08:00","@version":"1","message":"Mapped to com.genco.admin.controller.AdminLoginController#getAdminInfo()","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping","thread_name":"http-nio-20000-exec-7","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-07T09:23:08.311+08:00","@version":"1","message":"[DIGEST] 请求开始 - TraceId: 2ebc18ed0fa14a9cac92e31e2081a329, 类名: SystemStoreStaffController, 方法名: getList, 用户ID: 1, URI: /api/admin/system/store/staff/list, 方法: GET","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20000-exec-30","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:23:08.311+08:00","@version":"1","message":"[DETAIL] 请求详情: {\"traceId\":\"2ebc18ed0fa14a9cac92e31e2081a329\",\"method\":\"GET\",\"requestBody\":\"未记录\",\"clientIp\":\"127.0.0.1\",\"methodName\":\"getList\",\"requestParams\":{\"request\":{\"temp\":\"1754529788\",\"limit\":\"9999\",\"page\":\"1\"},\"method\":\"GET\",\"request_filter\":{},\"header\":{\"sec-fetch-mode\":\"cors\",\"referer\":\"http://localhost:9527/\",\"sec-fetch-site\":\"cross-site\",\"accept-language\":\"zh-CN,zh;q=0.9\",\"origin\":\"http://localhost:9527\",\"accept\":\"application/json, text/plain, */*\",\"sec-ch-ua\":\"\\\"Chromium\\\";v=\\\"9\\\", \\\"Not?A_Brand\\\";v=\\\"8\\\"\",\"sec-ch-ua-mobile\":\"?0\",\"sec-ch-ua-platform\":\"\\\"Windows\\\"\",\"host\":\"127.0.0.1:20000\",\"connection\":\"keep-alive\",\"authori-zation\":\"f314572531e74f07ae6e19bc2c87d804\",\"accept-encoding\":\"gzip, deflate, br, zstd\",\"user-agent\":\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit\",\"sec-fetch-dest\":\"empty\"},\"uri\":\"/api/admin/system/store/staff/list\",\"url\":\"http://127.0.0.1:20000/api/admin/system/store/staff/list\"},\"className\":\"com.genco.admin.controller.SystemStoreStaffController\",\"userAgent\":\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit\",\"userId\":1,\"uri\":\"/api/admin/system/store/staff/list\"}","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20000-exec-30","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:23:08.311+08:00","@version":"1","message":"[DIGEST] 请求开始 - TraceId: 787346fae7094e628ecbbc5916b68241, 类名: AdminLoginController, 方法名: getAdminInfo, 用户ID: 1, URI: /api/admin/getAdminInfoByToken, 方法: GET","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20000-exec-7","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:23:08.311+08:00","@version":"1","message":"[DETAIL] 请求详情: {\"traceId\":\"787346fae7094e628ecbbc5916b68241\",\"method\":\"GET\",\"requestBody\":\"未记录\",\"clientIp\":\"127.0.0.1\",\"methodName\":\"getAdminInfo\",\"requestParams\":{\"request\":{\"temp\":\"1754529788\",\"token\":\"f314572531e74f07ae6e19bc2c87d804\"},\"method\":\"GET\",\"request_filter\":{},\"header\":{\"sec-fetch-mode\":\"cors\",\"referer\":\"http://localhost:9527/\",\"sec-fetch-site\":\"cross-site\",\"accept-language\":\"zh-CN,zh;q=0.9\",\"origin\":\"http://localhost:9527\",\"accept\":\"application/json, text/plain, */*\",\"sec-ch-ua\":\"\\\"Chromium\\\";v=\\\"9\\\", \\\"Not?A_Brand\\\";v=\\\"8\\\"\",\"sec-ch-ua-mobile\":\"?0\",\"sec-ch-ua-platform\":\"\\\"Windows\\\"\",\"host\":\"127.0.0.1:20000\",\"connection\":\"keep-alive\",\"authori-zation\":\"f314572531e74f07ae6e19bc2c87d804\",\"accept-encoding\":\"gzip, deflate, br, zstd\",\"user-agent\":\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit\",\"sec-fetch-dest\":\"empty\"},\"uri\":\"/api/admin/getAdminInfoByToken\",\"url\":\"http://127.0.0.1:20000/api/admin/getAdminInfoByToken\"},\"className\":\"com.genco.admin.controller.AdminLoginController\",\"userAgent\":\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit\",\"userId\":1,\"uri\":\"/api/admin/getAdminInfoByToken\"}","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20000-exec-7","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:23:08.336+08:00","@version":"1","message":"Controller method：CommonResult com.genco.admin.controller.AdminLoginController.getAdminInfo()，prams：[]，cost time：10666700 ns，cost：10 ms","logger_name":"com.genco.admin.acpect.ControllerAspect","thread_name":"http-nio-20000-exec-7","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:23:08.336+08:00","@version":"1","message":"Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-20000-exec-7","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-07T09:23:08.336+08:00","@version":"1","message":"Writing [com.genco.common.response.CommonResult@2fa47484]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-20000-exec-7","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-07T09:23:08.344+08:00","@version":"1","message":"[DIGEST] 请求结束 - TraceId: 787346fae7094e628ecbbc5916b68241, 类名: AdminLoginController, 方法名: getAdminInfo, 用户ID: 1, 是否成功: true, 响应时间: 33ms, 状态码: 200","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20000-exec-7","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:23:08.344+08:00","@version":"1","message":"[DETAIL] 响应详情: {\"traceId\":\"787346fae7094e628ecbbc5916b68241\",\"requestBody\":\"未记录\",\"responseTime\":33,\"methodName\":\"getAdminInfo\",\"className\":\"com.genco.admin.controller.AdminLoginController\",\"userId\":1,\"isSuccess\":true,\"statusCode\":200}","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20000-exec-7","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:23:08.344+08:00","@version":"1","message":"Completed 200 OK","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-20000-exec-7","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-07T09:23:08.360+08:00","@version":"1","message":"GET \"/api/admin/getMenus?temp=1754529788\", parameters={masked}","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-20000-exec-5","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-07T09:23:08.361+08:00","@version":"1","message":"Mapped to com.genco.admin.controller.AdminLoginController#getMenus()","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping","thread_name":"http-nio-20000-exec-5","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-07T09:23:08.361+08:00","@version":"1","message":"[DIGEST] 请求开始 - TraceId: 33dc38360a8a4af8809d98b998357e36, 类名: AdminLoginController, 方法名: getMenus, 用户ID: 1, URI: /api/admin/getMenus, 方法: GET","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20000-exec-5","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:23:08.361+08:00","@version":"1","message":"[DETAIL] 请求详情: {\"traceId\":\"33dc38360a8a4af8809d98b998357e36\",\"method\":\"GET\",\"requestBody\":\"未记录\",\"clientIp\":\"127.0.0.1\",\"methodName\":\"getMenus\",\"requestParams\":{\"request\":{\"temp\":\"1754529788\"},\"method\":\"GET\",\"request_filter\":{},\"header\":{\"sec-fetch-mode\":\"cors\",\"referer\":\"http://localhost:9527/\",\"sec-fetch-site\":\"cross-site\",\"accept-language\":\"zh-CN,zh;q=0.9\",\"origin\":\"http://localhost:9527\",\"accept\":\"application/json, text/plain, */*\",\"sec-ch-ua\":\"\\\"Chromium\\\";v=\\\"9\\\", \\\"Not?A_Brand\\\";v=\\\"8\\\"\",\"sec-ch-ua-mobile\":\"?0\",\"sec-ch-ua-platform\":\"\\\"Windows\\\"\",\"host\":\"127.0.0.1:20000\",\"connection\":\"keep-alive\",\"authori-zation\":\"f314572531e74f07ae6e19bc2c87d804\",\"accept-encoding\":\"gzip, deflate, br, zstd\",\"user-agent\":\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit\",\"sec-fetch-dest\":\"empty\"},\"uri\":\"/api/admin/getMenus\",\"url\":\"http://127.0.0.1:20000/api/admin/getMenus\"},\"className\":\"com.genco.admin.controller.AdminLoginController\",\"userAgent\":\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit\",\"userId\":1,\"uri\":\"/api/admin/getMenus\"}","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20000-exec-5","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:23:08.593+08:00","@version":"1","message":"Controller method：CommonResult com.genco.admin.controller.SystemStoreStaffController.getList(Integer,PageParamRequest)，prams：[0, PageParamRequest(page=1, limit=9999)]，cost time：247310600 ns，cost：247 ms","logger_name":"com.genco.admin.acpect.ControllerAspect","thread_name":"http-nio-20000-exec-30","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:23:08.593+08:00","@version":"1","message":"Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-20000-exec-30","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-07T09:23:08.593+08:00","@version":"1","message":"Writing [com.genco.common.response.CommonResult@13495cda]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-20000-exec-30","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-07T09:23:08.597+08:00","@version":"1","message":"[DIGEST] 请求结束 - TraceId: 2ebc18ed0fa14a9cac92e31e2081a329, 类名: SystemStoreStaffController, 方法名: getList, 用户ID: 1, 是否成功: true, 响应时间: 284ms, 状态码: 200","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20000-exec-30","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:23:08.597+08:00","@version":"1","message":"[DETAIL] 响应详情: {\"traceId\":\"2ebc18ed0fa14a9cac92e31e2081a329\",\"requestBody\":\"未记录\",\"responseTime\":284,\"methodName\":\"getList\",\"className\":\"com.genco.admin.controller.SystemStoreStaffController\",\"userId\":1,\"isSuccess\":true,\"statusCode\":200}","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20000-exec-30","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:23:08.597+08:00","@version":"1","message":"Completed 200 OK","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-20000-exec-30","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-07T09:23:08.620+08:00","@version":"1","message":"Controller method：CommonResult com.genco.admin.controller.AdminLoginController.getMenus()，prams：[]，cost time：260134400 ns，cost：260 ms","logger_name":"com.genco.admin.acpect.ControllerAspect","thread_name":"http-nio-20000-exec-5","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:23:08.620+08:00","@version":"1","message":"Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-20000-exec-5","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-07T09:23:08.627+08:00","@version":"1","message":"Writing [com.genco.common.response.CommonResult@1aeff26c]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-20000-exec-5","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-07T09:23:08.631+08:00","@version":"1","message":"[DIGEST] 请求结束 - TraceId: 33dc38360a8a4af8809d98b998357e36, 类名: AdminLoginController, 方法名: getMenus, 用户ID: 1, 是否成功: true, 响应时间: 268ms, 状态码: 200","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20000-exec-5","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:23:08.631+08:00","@version":"1","message":"[DETAIL] 响应详情: {\"traceId\":\"33dc38360a8a4af8809d98b998357e36\",\"requestBody\":\"未记录\",\"responseTime\":268,\"methodName\":\"getMenus\",\"className\":\"com.genco.admin.controller.AdminLoginController\",\"userId\":1,\"isSuccess\":true,\"statusCode\":200}","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20000-exec-5","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:23:08.631+08:00","@version":"1","message":"Completed 200 OK","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-20000-exec-5","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-07T09:23:08.703+08:00","@version":"1","message":"GET \"/api/admin/system/config/info?formId=100&temp=1754529788\", parameters={masked}","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-20000-exec-1","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-07T09:23:08.703+08:00","@version":"1","message":"GET \"/api/admin/system/config/getuniq?key=site_logo_lefttop&temp=1754529788\", parameters={masked}","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-20000-exec-9","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-07T09:23:08.703+08:00","@version":"1","message":"GET \"/api/admin/system/config/getuniq?key=site_logo_square&temp=1754529788\", parameters={masked}","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-20000-exec-18","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-07T09:23:08.703+08:00","@version":"1","message":"Mapped to com.genco.admin.controller.SystemConfigController#justGetUniq(String)","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping","thread_name":"http-nio-20000-exec-18","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-07T09:23:08.703+08:00","@version":"1","message":"Mapped to com.genco.admin.controller.SystemConfigController#info(Integer)","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping","thread_name":"http-nio-20000-exec-1","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-07T09:23:08.703+08:00","@version":"1","message":"Mapped to com.genco.admin.controller.SystemConfigController#justGetUniq(String)","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping","thread_name":"http-nio-20000-exec-9","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-07T09:23:08.705+08:00","@version":"1","message":"[DIGEST] 请求开始 - TraceId: a36202a3c7a54e8399638f7c5c4b0863, 类名: SystemConfigController, 方法名: info, 用户ID: 1, URI: /api/admin/system/config/info, 方法: GET","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20000-exec-1","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:23:08.705+08:00","@version":"1","message":"[DIGEST] 请求开始 - TraceId: 28a28a7795c6439fad2a550e0d4c28fa, 类名: SystemConfigController, 方法名: justGetUniq, 用户ID: 1, URI: /api/admin/system/config/getuniq, 方法: GET","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20000-exec-9","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:23:08.705+08:00","@version":"1","message":"[DETAIL] 请求详情: {\"traceId\":\"a36202a3c7a54e8399638f7c5c4b0863\",\"method\":\"GET\",\"requestBody\":\"未记录\",\"clientIp\":\"127.0.0.1\",\"methodName\":\"info\",\"requestParams\":{\"request\":{\"formId\":\"100\",\"temp\":\"1754529788\"},\"method\":\"GET\",\"request_filter\":{},\"header\":{\"sec-fetch-mode\":\"cors\",\"referer\":\"http://localhost:9527/\",\"sec-fetch-site\":\"cross-site\",\"accept-language\":\"zh-CN,zh;q=0.9\",\"origin\":\"http://localhost:9527\",\"accept\":\"application/json, text/plain, */*\",\"sec-ch-ua\":\"\\\"Chromium\\\";v=\\\"9\\\", \\\"Not?A_Brand\\\";v=\\\"8\\\"\",\"sec-ch-ua-mobile\":\"?0\",\"sec-ch-ua-platform\":\"\\\"Windows\\\"\",\"host\":\"127.0.0.1:20000\",\"connection\":\"keep-alive\",\"authori-zation\":\"f314572531e74f07ae6e19bc2c87d804\",\"accept-encoding\":\"gzip, deflate, br, zstd\",\"user-agent\":\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit\",\"sec-fetch-dest\":\"empty\"},\"uri\":\"/api/admin/system/config/info\",\"url\":\"http://127.0.0.1:20000/api/admin/system/config/info\"},\"className\":\"com.genco.admin.controller.SystemConfigController\",\"userAgent\":\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit\",\"userId\":1,\"uri\":\"/api/admin/system/config/info\"}","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20000-exec-1","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:23:08.705+08:00","@version":"1","message":"[DETAIL] 请求详情: {\"traceId\":\"28a28a7795c6439fad2a550e0d4c28fa\",\"method\":\"GET\",\"requestBody\":\"未记录\",\"clientIp\":\"127.0.0.1\",\"methodName\":\"justGetUniq\",\"requestParams\":{\"request\":{\"temp\":\"1754529788\",\"key\":\"site_logo_lefttop\"},\"method\":\"GET\",\"request_filter\":{},\"header\":{\"sec-fetch-mode\":\"cors\",\"referer\":\"http://localhost:9527/\",\"sec-fetch-site\":\"cross-site\",\"accept-language\":\"zh-CN,zh;q=0.9\",\"origin\":\"http://localhost:9527\",\"accept\":\"application/json, text/plain, */*\",\"sec-ch-ua\":\"\\\"Chromium\\\";v=\\\"9\\\", \\\"Not?A_Brand\\\";v=\\\"8\\\"\",\"sec-ch-ua-mobile\":\"?0\",\"sec-ch-ua-platform\":\"\\\"Windows\\\"\",\"host\":\"127.0.0.1:20000\",\"connection\":\"keep-alive\",\"authori-zation\":\"f314572531e74f07ae6e19bc2c87d804\",\"accept-encoding\":\"gzip, deflate, br, zstd\",\"user-agent\":\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit\",\"sec-fetch-dest\":\"empty\"},\"uri\":\"/api/admin/system/config/getuniq\",\"url\":\"http://127.0.0.1:20000/api/admin/system/config/getuniq\"},\"className\":\"com.genco.admin.controller.SystemConfigController\",\"userAgent\":\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit\",\"userId\":1,\"uri\":\"/api/admin/system/config/getuniq\"}","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20000-exec-9","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:23:08.705+08:00","@version":"1","message":"[DIGEST] 请求开始 - TraceId: a0bf5cd3896b4fd3af9b8dae0af741bd, 类名: SystemConfigController, 方法名: justGetUniq, 用户ID: 1, URI: /api/admin/system/config/getuniq, 方法: GET","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20000-exec-18","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:23:08.705+08:00","@version":"1","message":"[DETAIL] 请求详情: {\"traceId\":\"a0bf5cd3896b4fd3af9b8dae0af741bd\",\"method\":\"GET\",\"requestBody\":\"未记录\",\"clientIp\":\"127.0.0.1\",\"methodName\":\"justGetUniq\",\"requestParams\":{\"request\":{\"temp\":\"1754529788\",\"key\":\"site_logo_square\"},\"method\":\"GET\",\"request_filter\":{},\"header\":{\"sec-fetch-mode\":\"cors\",\"referer\":\"http://localhost:9527/\",\"sec-fetch-site\":\"cross-site\",\"accept-language\":\"zh-CN,zh;q=0.9\",\"origin\":\"http://localhost:9527\",\"accept\":\"application/json, text/plain, */*\",\"sec-ch-ua\":\"\\\"Chromium\\\";v=\\\"9\\\", \\\"Not?A_Brand\\\";v=\\\"8\\\"\",\"sec-ch-ua-mobile\":\"?0\",\"sec-ch-ua-platform\":\"\\\"Windows\\\"\",\"host\":\"127.0.0.1:20000\",\"connection\":\"keep-alive\",\"authori-zation\":\"f314572531e74f07ae6e19bc2c87d804\",\"accept-encoding\":\"gzip, deflate, br, zstd\",\"user-agent\":\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit\",\"sec-fetch-dest\":\"empty\"},\"uri\":\"/api/admin/system/config/getuniq\",\"url\":\"http://127.0.0.1:20000/api/admin/system/config/getuniq\"},\"className\":\"com.genco.admin.controller.SystemConfigController\",\"userAgent\":\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit\",\"userId\":1,\"uri\":\"/api/admin/system/config/getuniq\"}","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20000-exec-18","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:23:08.812+08:00","@version":"1","message":"Controller method：CommonResult com.genco.admin.controller.SystemConfigController.justGetUniq(String)，prams：[site_logo_lefttop]，cost time：106150700 ns，cost：106 ms","logger_name":"com.genco.admin.acpect.ControllerAspect","thread_name":"http-nio-20000-exec-9","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:23:08.812+08:00","@version":"1","message":"Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-20000-exec-9","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-07T09:23:08.812+08:00","@version":"1","message":"Writing [com.genco.common.response.CommonResult@3204853f]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-20000-exec-9","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-07T09:23:08.821+08:00","@version":"1","message":"[DIGEST] 请求结束 - TraceId: 28a28a7795c6439fad2a550e0d4c28fa, 类名: SystemConfigController, 方法名: justGetUniq, 用户ID: 1, 是否成功: true, 响应时间: 109ms, 状态码: 200","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20000-exec-9","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:23:08.821+08:00","@version":"1","message":"[DETAIL] 响应详情: {\"traceId\":\"28a28a7795c6439fad2a550e0d4c28fa\",\"requestBody\":\"未记录\",\"responseTime\":109,\"methodName\":\"justGetUniq\",\"className\":\"com.genco.admin.controller.SystemConfigController\",\"userId\":1,\"isSuccess\":true,\"statusCode\":200}","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20000-exec-9","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:23:08.821+08:00","@version":"1","message":"Completed 200 OK","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-20000-exec-9","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-07T09:23:08.903+08:00","@version":"1","message":"Controller method：CommonResult com.genco.admin.controller.SystemConfigController.info(Integer)，prams：[100]，cost time：200484600 ns，cost：200 ms","logger_name":"com.genco.admin.acpect.ControllerAspect","thread_name":"http-nio-20000-exec-1","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:23:08.903+08:00","@version":"1","message":"Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-20000-exec-1","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-07T09:23:08.903+08:00","@version":"1","message":"Writing [com.genco.common.response.CommonResult@381645b4]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-20000-exec-1","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-07T09:23:08.911+08:00","@version":"1","message":"[DIGEST] 请求结束 - TraceId: a36202a3c7a54e8399638f7c5c4b0863, 类名: SystemConfigController, 方法名: info, 用户ID: 1, 是否成功: true, 响应时间: 200ms, 状态码: 200","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20000-exec-1","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:23:08.911+08:00","@version":"1","message":"[DETAIL] 响应详情: {\"traceId\":\"a36202a3c7a54e8399638f7c5c4b0863\",\"requestBody\":\"未记录\",\"responseTime\":200,\"methodName\":\"info\",\"className\":\"com.genco.admin.controller.SystemConfigController\",\"userId\":1,\"isSuccess\":true,\"statusCode\":200}","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20000-exec-1","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:23:08.911+08:00","@version":"1","message":"Completed 200 OK","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-20000-exec-1","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-07T09:23:08.944+08:00","@version":"1","message":"Controller method：CommonResult com.genco.admin.controller.SystemConfigController.justGetUniq(String)，prams：[site_logo_square]，cost time：229882000 ns，cost：229 ms","logger_name":"com.genco.admin.acpect.ControllerAspect","thread_name":"http-nio-20000-exec-18","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:23:08.944+08:00","@version":"1","message":"Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-20000-exec-18","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-07T09:23:08.944+08:00","@version":"1","message":"Writing [com.genco.common.response.CommonResult@234ccf00]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-20000-exec-18","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-07T09:23:08.944+08:00","@version":"1","message":"[DIGEST] 请求结束 - TraceId: a0bf5cd3896b4fd3af9b8dae0af741bd, 类名: SystemConfigController, 方法名: justGetUniq, 用户ID: 1, 是否成功: true, 响应时间: 241ms, 状态码: 200","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20000-exec-18","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:23:08.944+08:00","@version":"1","message":"[DETAIL] 响应详情: {\"traceId\":\"a0bf5cd3896b4fd3af9b8dae0af741bd\",\"requestBody\":\"未记录\",\"responseTime\":241,\"methodName\":\"justGetUniq\",\"className\":\"com.genco.admin.controller.SystemConfigController\",\"userId\":1,\"isSuccess\":true,\"statusCode\":200}","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20000-exec-18","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:23:08.944+08:00","@version":"1","message":"Completed 200 OK","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-20000-exec-18","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-07T09:23:12.712+08:00","@version":"1","message":"GET \"/api/admin/system/group/data/list?gid=74&temp=1754529792\", parameters={masked}","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-20000-exec-28","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-07T09:23:12.712+08:00","@version":"1","message":"GET \"/api/admin/brand/list?page=1&limit=20&name=&type=-1&temp=1754529792\", parameters={masked}","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-20000-exec-11","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-07T09:23:12.712+08:00","@version":"1","message":"Mapped to com.genco.admin.controller.StoreBrandController#getBrandList(Integer, String, PageParamRequest)","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping","thread_name":"http-nio-20000-exec-11","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-07T09:23:12.712+08:00","@version":"1","message":"Mapped to com.genco.admin.controller.SystemGroupDataController#getList(SystemGroupDataSearchRequest, PageParamRequest)","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping","thread_name":"http-nio-20000-exec-28","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-07T09:23:12.718+08:00","@version":"1","message":"[DIGEST] 请求开始 - TraceId: 3bfeff0e7b154b9a99658b7e7b22d7a5, 类名: StoreBrandController, 方法名: getBrandList, 用户ID: 1, URI: /api/admin/brand/list, 方法: GET","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20000-exec-11","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:23:12.719+08:00","@version":"1","message":"[DIGEST] 请求开始 - TraceId: a75a6c809a8b46d088ae9e08ed7e6523, 类名: SystemGroupDataController, 方法名: getList, 用户ID: 1, URI: /api/admin/system/group/data/list, 方法: GET","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20000-exec-28","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:23:12.719+08:00","@version":"1","message":"[DETAIL] 请求详情: {\"traceId\":\"3bfeff0e7b154b9a99658b7e7b22d7a5\",\"method\":\"GET\",\"requestBody\":\"未记录\",\"clientIp\":\"127.0.0.1\",\"methodName\":\"getBrandList\",\"requestParams\":{\"request\":{\"temp\":\"1754529792\",\"limit\":\"20\",\"name\":\"\",\"page\":\"1\",\"type\":\"-1\"},\"method\":\"GET\",\"request_filter\":{},\"header\":{\"sec-fetch-mode\":\"cors\",\"referer\":\"http://localhost:9527/\",\"sec-fetch-site\":\"cross-site\",\"accept-language\":\"zh-CN,zh;q=0.9\",\"origin\":\"http://localhost:9527\",\"accept\":\"application/json, text/plain, */*\",\"sec-ch-ua\":\"\\\"Chromium\\\";v=\\\"9\\\", \\\"Not?A_Brand\\\";v=\\\"8\\\"\",\"sec-ch-ua-mobile\":\"?0\",\"sec-ch-ua-platform\":\"\\\"Windows\\\"\",\"host\":\"127.0.0.1:20000\",\"connection\":\"keep-alive\",\"authori-zation\":\"f314572531e74f07ae6e19bc2c87d804\",\"accept-encoding\":\"gzip, deflate, br, zstd\",\"user-agent\":\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit\",\"sec-fetch-dest\":\"empty\"},\"uri\":\"/api/admin/brand/list\",\"url\":\"http://127.0.0.1:20000/api/admin/brand/list\"},\"className\":\"com.genco.admin.controller.StoreBrandController\",\"userAgent\":\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit\",\"userId\":1,\"uri\":\"/api/admin/brand/list\"}","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20000-exec-11","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:23:12.719+08:00","@version":"1","message":"[DETAIL] 请求详情: {\"traceId\":\"a75a6c809a8b46d088ae9e08ed7e6523\",\"method\":\"GET\",\"requestBody\":\"未记录\",\"clientIp\":\"127.0.0.1\",\"methodName\":\"getList\",\"requestParams\":{\"request\":{\"temp\":\"1754529792\",\"gid\":\"74\"},\"method\":\"GET\",\"request_filter\":{},\"header\":{\"sec-fetch-mode\":\"cors\",\"referer\":\"http://localhost:9527/\",\"sec-fetch-site\":\"cross-site\",\"accept-language\":\"zh-CN,zh;q=0.9\",\"origin\":\"http://localhost:9527\",\"accept\":\"application/json, text/plain, */*\",\"sec-ch-ua\":\"\\\"Chromium\\\";v=\\\"9\\\", \\\"Not?A_Brand\\\";v=\\\"8\\\"\",\"sec-ch-ua-mobile\":\"?0\",\"sec-ch-ua-platform\":\"\\\"Windows\\\"\",\"host\":\"127.0.0.1:20000\",\"connection\":\"keep-alive\",\"authori-zation\":\"f314572531e74f07ae6e19bc2c87d804\",\"accept-encoding\":\"gzip, deflate, br, zstd\",\"user-agent\":\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit\",\"sec-fetch-dest\":\"empty\"},\"uri\":\"/api/admin/system/group/data/list\",\"url\":\"http://127.0.0.1:20000/api/admin/system/group/data/list\"},\"className\":\"com.genco.admin.controller.SystemGroupDataController\",\"userAgent\":\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/111 SLBVPV/64-bit\",\"userId\":1,\"uri\":\"/api/admin/system/group/data/list\"}","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20000-exec-28","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:23:13.153+08:00","@version":"1","message":"Controller method：CommonResult com.genco.admin.controller.SystemGroupDataController.getList(SystemGroupDataSearchRequest,PageParamRequest)，prams：[SystemGroupDataSearchRequest(keywords=null, gid=74, status=null), PageParamRequest(page=1, limit=20)]，cost time：422685599 ns，cost：422 ms","logger_name":"com.genco.admin.acpect.ControllerAspect","thread_name":"http-nio-20000-exec-28","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:23:13.153+08:00","@version":"1","message":"Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-20000-exec-28","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-07T09:23:13.153+08:00","@version":"1","message":"Writing [com.genco.common.response.CommonResult@25b9e7ba]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-20000-exec-28","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-07T09:23:13.162+08:00","@version":"1","message":"[DIGEST] 请求结束 - TraceId: a75a6c809a8b46d088ae9e08ed7e6523, 类名: SystemGroupDataController, 方法名: getList, 用户ID: 1, 是否成功: true, 响应时间: 450ms, 状态码: 200","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20000-exec-28","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:23:13.163+08:00","@version":"1","message":"[DETAIL] 响应详情: {\"traceId\":\"a75a6c809a8b46d088ae9e08ed7e6523\",\"requestBody\":\"未记录\",\"responseTime\":450,\"methodName\":\"getList\",\"className\":\"com.genco.admin.controller.SystemGroupDataController\",\"userId\":1,\"isSuccess\":true,\"statusCode\":200}","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20000-exec-28","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:23:13.163+08:00","@version":"1","message":"Completed 200 OK","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-20000-exec-28","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-07T09:23:17.853+08:00","@version":"1","message":"Controller method：CommonResult com.genco.admin.controller.StoreBrandController.getBrandList(Integer,String,PageParamRequest)，prams：[-1, , PageParamRequest(page=1, limit=20)]，cost time：5127553400 ns，cost：5127 ms","logger_name":"com.genco.admin.acpect.ControllerAspect","thread_name":"http-nio-20000-exec-11","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:23:17.853+08:00","@version":"1","message":"Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-20000-exec-11","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-07T09:23:17.860+08:00","@version":"1","message":"Writing [com.genco.common.response.CommonResult@48bdc5e0]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-20000-exec-11","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-07T09:23:17.869+08:00","@version":"1","message":"[DIGEST] 请求结束 - TraceId: 3bfeff0e7b154b9a99658b7e7b22d7a5, 类名: StoreBrandController, 方法名: getBrandList, 用户ID: 1, 是否成功: true, 响应时间: 5157ms, 状态码: 200","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20000-exec-11","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:23:17.869+08:00","@version":"1","message":"[DETAIL] 响应详情: {\"traceId\":\"3bfeff0e7b154b9a99658b7e7b22d7a5\",\"requestBody\":\"未记录\",\"responseTime\":5157,\"methodName\":\"getBrandList\",\"className\":\"com.genco.admin.controller.StoreBrandController\",\"userId\":1,\"isSuccess\":true,\"statusCode\":200}","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20000-exec-11","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-07T09:23:17.869+08:00","@version":"1","message":"Completed 200 OK","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-20000-exec-11","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-07T09:23:56.857+08:00","@version":"1","message":"创建TikTok订单拉取初始任务，时间范围：Mon Aug 04 09:23:56 CST 2025 至 Thu Aug 07 09:23:56 CST 2025","logger_name":"com.genco.admin.task.order.OrderTiktokPullTask","thread_name":"crmeb-scheduled-task-pool-10","level":"INFO","level_value":20000}
