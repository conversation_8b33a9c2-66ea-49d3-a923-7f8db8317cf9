{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\eslint-loader\\index.js??ref--13-0!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\utils\\elementLocale.js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\utils\\elementLocale.js", "mtime": 1754530729621}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\babel.config.js", "mtime": 1754050582109}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754052680463}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754052678844}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\eslint-loader\\index.js", "mtime": 1754052677050}], "contextDependencies": [], "result": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.getAllElementLocales = getAllElementLocales;\nexports.getElementLocale = getElementLocale;\nexports.switchElementLocale = switchElementLocale;\nvar _vue = _interopRequireDefault(require(\"vue\"));\nvar _elementUi = _interopRequireDefault(require(\"element-ui\"));\nvar _zhCN = _interopRequireDefault(require(\"element-ui/lib/locale/lang/zh-CN\"));\nvar _en = _interopRequireDefault(require(\"element-ui/lib/locale/lang/en\"));\nvar _id = _interopRequireDefault(require(\"element-ui/lib/locale/lang/id\"));\nfunction _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }\n// Element UI 语言包映射\nvar elementLocales = {\n  'zh-CN': _zhCN.default,\n  'en': _en.default,\n  'id': _id.default\n};\n\n/**\n * 切换 Element UI 语言包\n * @param {string} locale - 语言代码 ('zh-CN', 'en', 'id')\n */\nfunction switchElementLocale(locale) {\n  var elementLocale = elementLocales[locale] || _zhCN.default;\n\n  // 动态切换 Element UI 语言包\n  _elementUi.default.locale(elementLocale);\n}\n\n/**\n * 获取 Element UI 语言包\n * @param {string} locale - 语言代码\n * @returns {Object} Element UI 语言包对象\n */\nfunction getElementLocale(locale) {\n  return elementLocales[locale] || _zhCN.default;\n}\n\n/**\n * 获取所有支持的语言包\n * @returns {Object} 所有语言包映射\n */\nfunction getAllElementLocales() {\n  return elementLocales;\n}", null]}