{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:17.129",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.l.ClasspathLoggingApplicationListener",
                    "message": "Application started with classpath: [file:/D:/Java/jre/lib/charsets.jar, file:/D:/Java/jre/lib/deploy.jar, file:/D:/Java/jre/lib/ext/access-bridge-64.jar, file:/D:/Java/jre/lib/ext/cldrdata.jar, file:/D:/Java/jre/lib/ext/dnsns.jar, file:/D:/Java/jre/lib/ext/jaccess.jar, file:/D:/Java/jre/lib/ext/localedata.jar, file:/D:/Java/jre/lib/ext/nashorn.jar, file:/D:/Java/jre/lib/ext/sunec.jar, file:/D:/Java/jre/lib/ext/sunjce_provider.jar, file:/D:/Java/jre/lib/ext/sunmscapi.jar, file:/D:/Java/jre/lib/ext/sunpkcs11.jar, file:/D:/Java/jre/lib/ext/zipfs.jar, file:/D:/Java/jre/lib/javaws.jar, file:/D:/Java/jre/lib/jce.jar, file:/D:/Java/jre/lib/jfr.jar, file:/D:/Java/jre/lib/jsse.jar, file:/D:/Java/jre/lib/management-agent.jar, file:/D:/Java/jre/lib/plugin.jar, file:/D:/Java/jre/lib/resources.jar, file:/D:/Java/jre/lib/rt.jar, file:/C:/Users/<USER>/Desktop/shop/api/genco-admin/target/classes/, file:/C:/Users/<USER>/Desktop/shop/api/genco-service/target/classes/, file:/C:/Users/<USER>/Desktop/shop/api/genco-common/target/classes/, file:/D:/MavenRepository/javax/servlet/javax.servlet-api/4.0.1/javax.servlet-api-4.0.1.jar, file:/D:/MavenRepository/javax/servlet/jstl/1.2/jstl-1.2.jar, file:/D:/MavenRepository/org/apache/tomcat/embed/tomcat-embed-jasper/9.0.33/tomcat-embed-jasper-9.0.33.jar, file:/D:/MavenRepository/org/apache/tomcat/embed/tomcat-embed-core/9.0.33/tomcat-embed-core-9.0.33.jar, file:/D:/MavenRepository/org/apache/tomcat/tomcat-annotations-api/9.0.33/tomcat-annotations-api-9.0.33.jar, file:/D:/MavenRepository/org/apache/tomcat/embed/tomcat-embed-el/9.0.33/tomcat-embed-el-9.0.33.jar, file:/D:/MavenRepository/org/eclipse/jdt/ecj/3.18.0/ecj-3.18.0.jar, file:/D:/MavenRepository/org/apache/tomcat/tomcat-jsp-api/9.0.33/tomcat-jsp-api-9.0.33.jar, file:/D:/MavenRepository/org/apache/tomcat/tomcat-el-api/9.0.33/tomcat-el-api-9.0.33.jar, file:/D:/MavenRepository/org/apache/tomcat/tomcat-servlet-api/9.0.33/tomcat-servlet-api-9.0.33.jar, file:/D:/MavenRepository/commons-configuration/commons-configuration/1.10/commons-configuration-1.10.jar, file:/D:/MavenRepository/commons-lang/commons-lang/2.6/commons-lang-2.6.jar, file:/D:/MavenRepository/commons-logging/commons-logging/1.1.1/commons-logging-1.1.1.jar, file:/D:/MavenRepository/org/apache/velocity/velocity/1.7/velocity-1.7.jar, file:/D:/MavenRepository/commons-collections/commons-collections/3.2.1/commons-collections-3.2.1.jar, file:/D:/MavenRepository/com/alibaba/fastjson/1.2.83/fastjson-1.2.83.jar, file:/D:/MavenRepository/com/alibaba/druid/1.1.20/druid-1.1.20.jar, file:/D:/MavenRepository/mysql/mysql-connector-java/8.0.29/mysql-connector-java-8.0.29.jar, file:/D:/MavenRepository/com/google/protobuf/protobuf-java/3.19.4/protobuf-java-3.19.4.jar, file:/D:/MavenRepository/org/projectlombok/lombok/1.18.12/lombok-1.18.12.jar, file:/D:/MavenRepository/com/baomidou/mybatis-plus-boot-starter/3.3.1/mybatis-plus-boot-starter-3.3.1.jar, file:/D:/MavenRepository/com/baomidou/mybatis-plus/3.3.1/mybatis-plus-3.3.1.jar, file:/D:/MavenRepository/com/baomidou/mybatis-plus-extension/3.3.1/mybatis-plus-extension-3.3.1.jar, file:/D:/MavenRepository/com/baomidou/mybatis-plus-core/3.3.1/mybatis-plus-core-3.3.1.jar, file:/D:/MavenRepository/com/baomidou/mybatis-plus-annotation/3.3.1/mybatis-plus-annotation-3.3.1.jar, file:/D:/MavenRepository/com/baomidou/mybatis-plus-generator/3.3.1/mybatis-plus-generator-3.3.1.jar, file:/D:/MavenRepository/io/swagger/swagger-annotations/1.5.21/swagger-annotations-1.5.21.jar, file:/D:/MavenRepository/io/springfox/springfox-swagger2/2.9.2/springfox-swagger2-2.9.2.jar, file:/D:/MavenRepository/io/springfox/springfox-spi/2.9.2/springfox-spi-2.9.2.jar, file:/D:/MavenRepository/io/springfox/springfox-core/2.9.2/springfox-core-2.9.2.jar, file:/D:/MavenRepository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar, file:/D:/MavenRepository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar, file:/D:/MavenRepository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar, file:/D:/MavenRepository/com/google/guava/guava/20.0/guava-20.0.jar, file:/D:/MavenRepository/com/fasterxml/classmate/1.5.1/classmate-1.5.1.jar, file:/D:/MavenRepository/org/springframework/plugin/spring-plugin-core/1.2.0.RELEASE/spring-plugin-core-1.2.0.RELEASE.jar, file:/D:/MavenRepository/org/springframework/plugin/spring-plugin-metadata/1.2.0.RELEASE/spring-plugin-metadata-1.2.0.RELEASE.jar, file:/D:/MavenRepository/org/mapstruct/mapstruct/1.2.0.Final/mapstruct-1.2.0.Final.jar, file:/D:/MavenRepository/io/swagger/swagger-models/1.5.22/swagger-models-1.5.22.jar, file:/D:/MavenRepository/com/fasterxml/jackson/core/jackson-annotations/2.10.3/jackson-annotations-2.10.3.jar, file:/D:/MavenRepository/com/github/xiaoymin/swagger-bootstrap-ui/1.9.3/swagger-bootstrap-ui-1.9.3.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-autoconfigure/2.2.6.RELEASE/spring-boot-autoconfigure-2.2.6.RELEASE.jar, file:/D:/MavenRepository/ch/qos/logback/logback-classic/1.2.3/logback-classic-1.2.3.jar, file:/D:/MavenRepository/ch/qos/logback/logback-core/1.2.3/logback-core-1.2.3.jar, file:/D:/MavenRepository/net/logstash/logback/logstash-logback-encoder/5.3/logstash-logback-encoder-5.3.jar, file:/D:/MavenRepository/com/fasterxml/jackson/core/jackson-databind/2.10.3/jackson-databind-2.10.3.jar, file:/D:/MavenRepository/com/fasterxml/jackson/core/jackson-core/2.10.3/jackson-core-2.10.3.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter-web/2.2.6.RELEASE/spring-boot-starter-web-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter-json/2.2.6.RELEASE/spring-boot-starter-json-2.2.6.RELEASE.jar, file:/D:/MavenRepository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.10.3/jackson-datatype-jdk8-2.10.3.jar, file:/D:/MavenRepository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.10.3/jackson-datatype-jsr310-2.10.3.jar, file:/D:/MavenRepository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.10.3/jackson-module-parameter-names-2.10.3.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter-tomcat/2.2.6.RELEASE/spring-boot-starter-tomcat-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/apache/tomcat/embed/tomcat-embed-websocket/9.0.33/tomcat-embed-websocket-9.0.33.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter-validation/2.2.6.RELEASE/spring-boot-starter-validation-2.2.6.RELEASE.jar, file:/D:/MavenRepository/jakarta/validation/jakarta.validation-api/2.0.2/jakarta.validation-api-2.0.2.jar, file:/D:/MavenRepository/org/hibernate/validator/hibernate-validator/6.0.18.Final/hibernate-validator-6.0.18.Final.jar, file:/D:/MavenRepository/org/jboss/logging/jboss-logging/3.4.1.Final/jboss-logging-3.4.1.Final.jar, file:/D:/MavenRepository/org/springframework/spring-web/5.2.5.RELEASE/spring-web-5.2.5.RELEASE.jar, file:/D:/MavenRepository/org/springframework/spring-webmvc/5.2.5.RELEASE/spring-webmvc-5.2.5.RELEASE.jar, file:/D:/MavenRepository/org/springframework/spring-expression/5.2.5.RELEASE/spring-expression-5.2.5.RELEASE.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter-data-redis/2.2.0.RELEASE/spring-boot-starter-data-redis-2.2.0.RELEASE.jar, file:/D:/MavenRepository/org/springframework/data/spring-data-redis/2.2.6.RELEASE/spring-data-redis-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/springframework/data/spring-data-keyvalue/2.2.6.RELEASE/spring-data-keyvalue-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/springframework/spring-tx/5.2.5.RELEASE/spring-tx-5.2.5.RELEASE.jar, file:/D:/MavenRepository/org/springframework/spring-oxm/5.2.5.RELEASE/spring-oxm-5.2.5.RELEASE.jar, file:/D:/MavenRepository/org/springframework/spring-context-support/5.2.5.RELEASE/spring-context-support-5.2.5.RELEASE.jar, file:/D:/MavenRepository/io/lettuce/lettuce-core/5.2.2.RELEASE/lettuce-core-5.2.2.RELEASE.jar, file:/D:/MavenRepository/io/netty/netty-common/4.1.48.Final/netty-common-4.1.48.Final.jar, file:/D:/MavenRepository/io/netty/netty-handler/4.1.48.Final/netty-handler-4.1.48.Final.jar, file:/D:/MavenRepository/io/netty/netty-resolver/4.1.48.Final/netty-resolver-4.1.48.Final.jar, file:/D:/MavenRepository/io/netty/netty-buffer/4.1.48.Final/netty-buffer-4.1.48.Final.jar, file:/D:/MavenRepository/io/netty/netty-codec/4.1.48.Final/netty-codec-4.1.48.Final.jar, file:/D:/MavenRepository/io/netty/netty-transport/4.1.48.Final/netty-transport-4.1.48.Final.jar, file:/D:/MavenRepository/io/projectreactor/reactor-core/3.3.4.RELEASE/reactor-core-3.3.4.RELEASE.jar, file:/D:/MavenRepository/org/reactivestreams/reactive-streams/1.0.3/reactive-streams-1.0.3.jar, file:/D:/MavenRepository/redis/clients/jedis/3.1.0/jedis-3.1.0.jar, file:/D:/MavenRepository/org/apache/commons/commons-pool2/2.7.0/commons-pool2-2.7.0.jar, file:/D:/MavenRepository/com/github/pagehelper/pagehelper-spring-boot-starter/1.2.5/pagehelper-spring-boot-starter-1.2.5.jar, file:/D:/MavenRepository/com/github/pagehelper/pagehelper-spring-boot-autoconfigure/1.2.5/pagehelper-spring-boot-autoconfigure-1.2.5.jar, file:/D:/MavenRepository/com/github/pagehelper/pagehelper/5.1.4/pagehelper-5.1.4.jar, file:/D:/MavenRepository/com/github/jsqlparser/jsqlparser/1.0/jsqlparser-1.0.jar, file:/D:/MavenRepository/javax/validation/validation-api/1.1.0.Final/validation-api-1.1.0.Final.jar, file:/D:/MavenRepository/org/springframework/data/spring-data-commons/2.2.6.RELEASE/spring-data-commons-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/springframework/spring-beans/5.2.5.RELEASE/spring-beans-5.2.5.RELEASE.jar, file:/D:/MavenRepository/cn/hutool/hutool-all/4.5.7/hutool-all-4.5.7.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter-actuator/2.2.6.RELEASE/spring-boot-starter-actuator-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-actuator-autoconfigure/2.2.6.RELEASE/spring-boot-actuator-autoconfigure-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-actuator/2.2.6.RELEASE/spring-boot-actuator-2.2.6.RELEASE.jar, file:/D:/MavenRepository/io/micrometer/micrometer-core/1.3.6/micrometer-core-1.3.6.jar, file:/D:/MavenRepository/org/hdrhistogram/HdrHistogram/2.1.11/HdrHistogram-2.1.11.jar, file:/D:/MavenRepository/org/latencyutils/LatencyUtils/2.0.3/LatencyUtils-2.0.3.jar, file:/D:/MavenRepository/org/apache/httpcomponents/httpclient/4.5.6/httpclient-4.5.6.jar, file:/D:/MavenRepository/org/apache/httpcomponents/httpcore/4.4.13/httpcore-4.4.13.jar, file:/D:/MavenRepository/commons-codec/commons-codec/1.13/commons-codec-1.13.jar, file:/D:/MavenRepository/org/apache/commons/commons-lang3/3.5/commons-lang3-3.5.jar, file:/D:/MavenRepository/org/apache/poi/poi/3.17/poi-3.17.jar, file:/D:/MavenRepository/org/apache/commons/commons-collections4/4.1/commons-collections4-4.1.jar, file:/D:/MavenRepository/org/apache/poi/poi-ooxml/3.17/poi-ooxml-3.17.jar, file:/D:/MavenRepository/org/apache/poi/poi-ooxml-schemas/3.17/poi-ooxml-schemas-3.17.jar, file:/D:/MavenRepository/org/apache/xmlbeans/xmlbeans/2.6.0/xmlbeans-2.6.0.jar, file:/D:/MavenRepository/com/github/virtuald/curvesapi/1.04/curvesapi-1.04.jar, file:/D:/MavenRepository/commons-fileupload/commons-fileupload/1.3.3/commons-fileupload-1.3.3.jar, file:/D:/MavenRepository/commons-io/commons-io/2.4/commons-io-2.4.jar, file:/D:/MavenRepository/net/coobird/thumbnailator/0.4.8/thumbnailator-0.4.8.jar, file:/D:/MavenRepository/com/aliyun/oss/aliyun-sdk-oss/3.5.0/aliyun-sdk-oss-3.5.0.jar, file:/D:/MavenRepository/org/jdom/jdom/1.1/jdom-1.1.jar, file:/D:/MavenRepository/org/codehaus/jettison/jettison/1.1/jettison-1.1.jar, file:/D:/MavenRepository/stax/stax-api/1.0.1/stax-api-1.0.1.jar, file:/D:/MavenRepository/com/aliyun/aliyun-java-sdk-core/3.4.0/aliyun-java-sdk-core-3.4.0.jar, file:/D:/MavenRepository/com/aliyun/aliyun-java-sdk-ram/3.0.0/aliyun-java-sdk-ram-3.0.0.jar, file:/D:/MavenRepository/com/aliyun/aliyun-java-sdk-sts/3.0.0/aliyun-java-sdk-sts-3.0.0.jar, file:/D:/MavenRepository/com/aliyun/aliyun-java-sdk-ecs/4.2.0/aliyun-java-sdk-ecs-4.2.0.jar, file:/D:/MavenRepository/com/qcloud/cos_api/5.6.22/cos_api-5.6.22.jar, file:/D:/MavenRepository/joda-time/joda-time/2.10.5/joda-time-2.10.5.jar, file:/D:/MavenRepository/org/bouncycastle/bcprov-jdk15on/1.64/bcprov-jdk15on-1.64.jar, file:/D:/MavenRepository/com/qiniu/qiniu-java-sdk/7.2.28/qiniu-java-sdk-7.2.28.jar, file:/D:/MavenRepository/com/google/code/gson/gson/2.8.6/gson-2.8.6.jar, file:/D:/MavenRepository/dom4j/dom4j/1.6.1/dom4j-1.6.1.jar, file:/D:/MavenRepository/xml-apis/xml-apis/1.0.b2/xml-apis-1.0.b2.jar, file:/D:/MavenRepository/com/thoughtworks/xstream/xstream/1.4.18/xstream-1.4.18.jar, file:/D:/MavenRepository/io/github/x-stream/mxparser/1.2.2/mxparser-1.2.2.jar, file:/D:/MavenRepository/xmlpull/xmlpull/1.1.3.1/xmlpull-1.1.3.1.jar, file:/D:/MavenRepository/org/mongodb/mongodb-driver-core/3.8.2/mongodb-driver-core-3.8.2.jar, file:/D:/MavenRepository/org/mongodb/bson/3.11.2/bson-3.11.2.jar, file:/D:/MavenRepository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar, file:/D:/MavenRepository/org/apache/httpcomponents/httpmime/4.5.2/httpmime-4.5.2.jar, file:/D:/MavenRepository/com/google/zxing/core/3.3.3/core-3.3.3.jar, file:/D:/MavenRepository/com/google/zxing/javase/3.3.3/javase-3.3.3.jar, file:/D:/MavenRepository/com/beust/jcommander/1.72/jcommander-1.72.jar, file:/D:/MavenRepository/com/github/jai-imageio/jai-imageio-core/1.4.0/jai-imageio-core-1.4.0.jar, file:/D:/MavenRepository/com/belerweb/pinyin4j/2.5.0/pinyin4j-2.5.0.jar, file:/D:/MavenRepository/io/jsonwebtoken/jjwt/0.9.1/jjwt-0.9.1.jar, file:/D:/MavenRepository/com/auth0/jwks-rsa/0.9.0/jwks-rsa-0.9.0.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter-security/2.2.6.RELEASE/spring-boot-starter-security-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/springframework/security/spring-security-config/5.2.2.RELEASE/spring-security-config-5.2.2.RELEASE.jar, file:/D:/MavenRepository/org/springframework/security/spring-security-core/5.2.2.RELEASE/spring-security-core-5.2.2.RELEASE.jar, file:/D:/MavenRepository/org/springframework/security/spring-security-web/5.2.2.RELEASE/spring-security-web-5.2.2.RELEASE.jar, file:/D:/MavenRepository/com/tiktokshop/open-sdk-java/1.0.0/open-sdk-java-1.0.0.jar, file:/D:/MavenRepository/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar, file:/D:/MavenRepository/io/gsonfire/gson-fire/1.9.0/gson-fire-1.9.0.jar, file:/D:/MavenRepository/org/openapitools/jackson-databind-nullable/0.2.6/jackson-databind-nullable-0.2.6.jar, file:/D:/MavenRepository/javax/ws/rs/jsr311-api/1.1.1/jsr311-api-1.1.1.jar, file:/D:/MavenRepository/javax/ws/rs/javax.ws.rs-api/2.1.1/javax.ws.rs-api-2.1.1.jar, file:/D:/MavenRepository/com/squareup/okhttp3/okhttp/4.11.0/okhttp-4.11.0.jar, file:/D:/MavenRepository/com/squareup/okio/okio/3.2.0/okio-3.2.0.jar, file:/D:/MavenRepository/com/squareup/okio/okio-jvm/3.2.0/okio-jvm-3.2.0.jar, file:/D:/MavenRepository/org/jetbrains/kotlin/kotlin-stdlib/1.3.71/kotlin-stdlib-1.3.71.jar, file:/D:/MavenRepository/org/jetbrains/kotlin/kotlin-stdlib-common/1.3.71/kotlin-stdlib-common-1.3.71.jar, file:/D:/MavenRepository/org/jetbrains/annotations/13.0/annotations-13.0.jar, file:/D:/MavenRepository/org/jetbrains/kotlin/kotlin-stdlib-jdk8/1.3.71/kotlin-stdlib-jdk8-1.3.71.jar, file:/D:/MavenRepository/org/jetbrains/kotlin/kotlin-stdlib-jdk7/1.3.71/kotlin-stdlib-jdk7-1.3.71.jar, file:/D:/MavenRepository/com/squareup/okhttp3/logging-interceptor/4.11.0/logging-interceptor-4.11.0.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter-aop/2.2.6.RELEASE/spring-boot-starter-aop-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/springframework/spring-aop/5.2.5.RELEASE/spring-aop-5.2.5.RELEASE.jar, file:/D:/MavenRepository/org/aspectj/aspectjweaver/1.9.5/aspectjweaver-1.9.5.jar, file:/D:/MavenRepository/org/mybatis/spring/boot/mybatis-spring-boot-starter/2.2.0/mybatis-spring-boot-starter-2.2.0.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter-jdbc/2.2.6.RELEASE/spring-boot-starter-jdbc-2.2.6.RELEASE.jar, file:/D:/MavenRepository/com/zaxxer/HikariCP/3.4.2/HikariCP-3.4.2.jar, file:/D:/MavenRepository/org/springframework/spring-jdbc/5.2.5.RELEASE/spring-jdbc-5.2.5.RELEASE.jar, file:/D:/MavenRepository/org/mybatis/spring/boot/mybatis-spring-boot-autoconfigure/2.2.0/mybatis-spring-boot-autoconfigure-2.2.0.jar, file:/D:/MavenRepository/org/mybatis/mybatis/3.5.7/mybatis-3.5.7.jar, file:/D:/MavenRepository/org/mybatis/mybatis-spring/2.0.6/mybatis-spring-2.0.6.jar, file:/D:/MavenRepository/net/minidev/json-smart/2.3/json-smart-2.3.jar, file:/D:/MavenRepository/net/minidev/accessors-smart/1.2/accessors-smart-1.2.jar, file:/D:/MavenRepository/org/ow2/asm/asm/5.0.4/asm-5.0.4.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter/2.2.6.RELEASE/spring-boot-starter-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot/2.2.6.RELEASE/spring-boot-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/springframework/spring-context/5.2.5.RELEASE/spring-context-5.2.5.RELEASE.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter-logging/2.2.6.RELEASE/spring-boot-starter-logging-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/apache/logging/log4j/log4j-to-slf4j/2.12.1/log4j-to-slf4j-2.12.1.jar, file:/D:/MavenRepository/org/apache/logging/log4j/log4j-api/2.12.1/log4j-api-2.12.1.jar, file:/D:/MavenRepository/org/slf4j/jul-to-slf4j/1.7.30/jul-to-slf4j-1.7.30.jar, file:/D:/MavenRepository/jakarta/annotation/jakarta.annotation-api/1.3.5/jakarta.annotation-api-1.3.5.jar, file:/D:/MavenRepository/org/yaml/snakeyaml/1.25/snakeyaml-1.25.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-test/2.2.6.RELEASE/spring-boot-test-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/slf4j/slf4j-api/1.7.30/slf4j-api-1.7.30.jar, file:/D:/MavenRepository/org/springframework/spring-core/5.2.5.RELEASE/spring-core-5.2.5.RELEASE.jar, file:/D:/MavenRepository/org/springframework/spring-jcl/5.2.5.RELEASE/spring-jcl-5.2.5.RELEASE.jar, file:/D:/MavenRepository/org/springframework/spring-test/5.2.5.RELEASE/spring-test-5.2.5.RELEASE.jar, file:/D:/MavenRepository/net/bytebuddy/byte-buddy/1.10.8/byte-buddy-1.10.8.jar, file:/D:/Idea/IntelliJ%20IDEA%202025.1.4.1/lib/idea_rt.jar, file:/C:/Users/<USER>/AppData/Local/JetBrains/IntelliJIdea2025.1/captureAgent/debugger-agent.jar]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:17.302",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.springframework.boot.SpringApplication",
                    "message": "Loading source class com.genco.admin.GencoAdminApplication" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:17.393",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.c.ConfigFileApplicationListener",
                    "message": "Activated activeProfiles prod" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:17.393",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.c.ConfigFileApplicationListener",
                    "message": "Loaded config file 'file:/C:/Users/<USER>/Desktop/shop/api/genco-admin/target/classes/application.yml' (classpath:/application.yml)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:17.393",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.c.ConfigFileApplicationListener",
                    "message": "Profiles already activated, '[prod]' will not be applied" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:17.393",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.c.ConfigFileApplicationListener",
                    "message": "Loaded config file 'file:/C:/Users/<USER>/Desktop/shop/api/genco-admin/target/classes/application-prod.yml' (classpath:/application-prod.yml) for profile prod" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:17.394",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext",
                    "message": "Refreshing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@3e2822" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:20.532",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.e.t.TomcatServletWebServerFactory",
                    "message": "Code archive: D:\MavenRepository\org\springframework\boot\spring-boot\2.2.6.RELEASE\spring-boot-2.2.6.RELEASE.jar" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:20.533",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.e.t.TomcatServletWebServerFactory",
                    "message": "Code archive: D:\MavenRepository\org\springframework\boot\spring-boot\2.2.6.RELEASE\spring-boot-2.2.6.RELEASE.jar" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:20.533",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.e.t.TomcatServletWebServerFactory",
                    "message": "None of the document roots [src/main/webapp, public, static] point to a directory and will be ignored." }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:20.878",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.web.context.ContextLoader",
                    "message": "Published root WebApplicationContext as ServletContext attribute with name [org.springframework.web.context.WebApplicationContext.ROOT]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:21.585",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.ServletContextInitializerBeans",
                    "message": "Mapping filters: filterRegistrationBean urls=[/*] order=-2147483647, springSecurityFilterChain urls=[/*] order=-100, filterRegistrationBean urls=[/*] order=2147483647, filterRegistrationBean urls=[/api/admin/*, /api/front/*] order=2147483647, characterEncodingFilter urls=[/*] order=-2147483648, formContentFilter urls=[/*] order=-9900, requestContextFilter urls=[/*] order=-105, corsFilter urls=[/*] order=2147483647, jwtAuthenticationTokenFilter urls=[/*] order=2147483647" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:21.586",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.ServletContextInitializerBeans",
                    "message": "Mapping servlets: statViewServlet urls=[/druid/*], dispatcherServlet urls=[/]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:21.620",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.a.m.w.servlet.WebMvcMetricsFilter",
                    "message": "Filter 'webMvcMetricsFilter' configured for use" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:21.620",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.f.OrderedRequestContextFilter",
                    "message": "Filter 'requestContextFilter' configured for use" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:21.620",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.springframework.web.filter.CorsFilter",
                    "message": "Filter 'corsFilter' configured for use" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:21.621",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.f.OrderedCharacterEncodingFilter",
                    "message": "Filter 'characterEncodingFilter' configured for use" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:21.621",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.DelegatingFilterProxyRegistrationBean$1",
                    "message": "Filter 'springSecurityFilterChain' configured for use" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:21.621",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.f.OrderedFormContentFilter",
                    "message": "Filter 'formContentFilter' configured for use" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:29.607",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "298 mappings in 'requestMappingHandlerMapping'" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:30.646",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.a.SpringApplicationAdminMXBeanRegistrar$SpringApplicationAdmin",
                    "message": "Application Admin MBean registered with name 'org.springframework.boot:type=Admin,name=SpringApplication'" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:30.668",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerAdapter",
                    "message": "ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:30.823",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.w.s.handler.SimpleUrlHandlerMapping",
                    "message": "Patterns [/webjars/**, /**, /doc.html, /crmebimage/**] in 'resourceHandlerMapping'" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:30.860",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver",
                    "message": "ControllerAdvice beans: 1 @ExceptionHandler, 1 ResponseBodyAdvice" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:36.516",
                    "level": "DEBUG",
                    "thread": "RMI TCP Connection(8)-************",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Detected StandardServletMultipartResolver" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:36.546",
                    "level": "DEBUG",
                    "thread": "RMI TCP Connection(8)-************",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:19:38.997",
                    "level": "DEBUG",
                    "thread": "RMI TCP Connection(5)-************",
                    "class": "o.springframework.jdbc.core.JdbcTemplate",
                    "message": "Executing SQL query [/* ping */ SELECT 1]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:22:57.931",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-19",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getLoginPic?temp=**********", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:22:57.932",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-6",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/validate/code/get?temp=**********", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:22:58.001",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-19",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getLoginPic()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:22:58.001",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-6",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.ValidateCodeController#get()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:22:59.007",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-6",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:22:59.008",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-6",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@56f1e1f9]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:22:59.066",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-6",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:00.009",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-19",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:00.011",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-19",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@5559d64a]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:00.013",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-19",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:05.610",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-14",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "POST "/api/admin/login", parameters={}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:05.611",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#SystemAdminLogin(SystemAdminLoginRequest, HttpServletRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:05.773",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Read "application/json;charset=UTF-8" to [SystemAdminLoginRequest(account=admin, pwd=BzX36YjvNR8o, key=3e0c35242e766e78e3660855a2ad46c2, code= (truncated)...]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.228",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.228",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@7fbfb4fc]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.228",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-14",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.311",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-30",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/store/staff/list?page=1&limit=9999&temp=1754529788", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.311",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-7",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getAdminInfoByToken?token=f314572531e74f07ae6e19bc2c87d804&temp=1754529788", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.311",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-30",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemStoreStaffController#getList(Integer, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.311",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-7",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getAdminInfo()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.336",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-7",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.336",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-7",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@2fa47484]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.344",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-7",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.360",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-5",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getMenus?temp=1754529788", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.361",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-5",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getMenus()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.593",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-30",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.593",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-30",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@13495cda]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.597",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-30",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.620",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-5",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.627",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-5",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@1aeff26c]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.631",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-5",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.703",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-1",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/info?formId=100&temp=1754529788", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.703",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_lefttop&temp=1754529788", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.703",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_square&temp=1754529788", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.703",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.703",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-1",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#info(Integer)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.703",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.812",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.812",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@3204853f]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.821",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.903",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-1",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.903",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-1",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@381645b4]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.911",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-1",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.944",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.944",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@234ccf00]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:08.944",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:12.712",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-28",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/group/data/list?gid=74&temp=1754529792", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:12.712",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-11",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/brand/list?page=1&limit=20&name=&type=-1&temp=1754529792", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:12.712",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreBrandController#getBrandList(Integer, String, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:12.712",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-28",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemGroupDataController#getList(SystemGroupDataSearchRequest, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:13.153",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-28",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:13.153",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-28",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@25b9e7ba]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:13.163",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-28",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:17.853",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:17.860",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@48bdc5e0]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:23:17.869",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-11",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:11.674",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-20",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getAdminInfoByToken?token=f314572531e74f07ae6e19bc2c87d804&temp=1754530331", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:11.677",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-20",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getAdminInfo()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:11.691",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-20",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:11.692",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-20",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@7ebe4155]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:11.695",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-20",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:11.712",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-22",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getMenus?temp=1754530331", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:11.713",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getMenus()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:11.953",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:11.953",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@4de1365b]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:11.956",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-22",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:12.409",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-14",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_lefttop&temp=1754530332", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:12.411",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:12.411",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-12",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_square&temp=1754530332", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:12.411",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-13",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/brand/list?page=1&limit=20&name=&type=-1&temp=1754530332", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:12.412",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-12",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:12.412",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-7",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/group/data/list?gid=74&temp=1754530332", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:12.412",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-13",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreBrandController#getBrandList(Integer, String, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:12.414",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-7",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemGroupDataController#getList(SystemGroupDataSearchRequest, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:12.645",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:12.645",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@2bd0569]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:12.649",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-14",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:12.768",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-7",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:12.768",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-7",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@3442591a]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:12.773",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-7",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:12.867",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-12",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:12.867",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-12",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@3c4b7373]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:12.870",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-12",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:17.682",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-13",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:17.682",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-13",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@3f862829]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:17.685",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-13",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:31.404",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-3",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getAdminInfoByToken?token=f314572531e74f07ae6e19bc2c87d804&temp=1754530351", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:31.405",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-3",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getAdminInfo()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:31.410",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-3",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:31.410",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-3",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@1eb53a04]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:31.413",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-3",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:31.435",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-2",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getMenus?temp=1754530351", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:31.436",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-2",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getMenus()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:31.678",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-2",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:31.679",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-2",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@451366b0]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:31.681",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-2",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:32.079",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-16",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/group/data/list?gid=74&temp=1754530352", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:32.079",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-28",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_square&temp=1754530352", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:32.080",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-16",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemGroupDataController#getList(SystemGroupDataSearchRequest, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:32.080",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-28",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:32.080",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-11",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/brand/list?page=1&limit=20&name=&type=-1&temp=1754530352", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:32.081",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreBrandController#getBrandList(Integer, String, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:32.083",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-27",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_lefttop&temp=1754530352", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:32.084",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-27",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:32.244",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-16",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:32.244",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-16",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@18af2f47]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:32.248",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-16",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:32.290",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-27",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:32.290",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-27",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@22ed94f6]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:32.293",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-27",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:32.314",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-28",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:32.314",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-28",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@3a88b2aa]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:32.317",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-28",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:34.327",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:34.327",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@7d8dfdba]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:32:34.330",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-11",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:33:02.194",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-25",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getAdminInfoByToken?token=f314572531e74f07ae6e19bc2c87d804&temp=1754530382", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:33:02.196",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-25",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getAdminInfo()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:33:02.200",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-25",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:33:02.201",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-25",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@7aeda640]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:33:02.204",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-25",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:33:02.216",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-23",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getMenus?temp=1754530382", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:33:02.217",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-23",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getMenus()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:33:02.326",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-23",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:33:02.326",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-23",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@45ff17e5]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:33:02.328",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-23",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:35:14.634",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-15",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/validate/code/get?temp=**********", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:35:14.634",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-6",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getLoginPic?temp=**********", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:35:14.634",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-15",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.ValidateCodeController#get()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:35:14.634",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-6",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getLoginPic()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:35:14.656",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-15",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:35:14.656",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-15",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@2e475856]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:35:14.657",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-15",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:35:15.110",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-6",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:35:15.110",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-6",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@2bf54dc0]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:35:15.112",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-6",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:35:39.302",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-13",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/validate/code/get?temp=**********", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:35:39.302",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-12",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getLoginPic?temp=**********", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:35:39.304",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-13",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.ValidateCodeController#get()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:35:39.304",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-12",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getLoginPic()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:35:39.323",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-13",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:35:39.323",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-13",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@319718a5]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:35:39.324",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-13",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:35:39.790",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-12",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:35:39.791",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-12",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@4ff88fce]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:35:39.792",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-12",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:35:59.645",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-5",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getLoginPic?temp=**********", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:35:59.645",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-3",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/validate/code/get?temp=**********", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:35:59.645",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-5",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getLoginPic()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:35:59.645",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-3",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.ValidateCodeController#get()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:35:59.655",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-3",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:35:59.655",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-3",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@2cb579e8]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:35:59.656",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-3",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:00.117",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-5",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:00.118",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-5",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@4b293264]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:00.118",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-5",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:06.664",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-29",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/validate/code/get?temp=**********", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:06.664",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getLoginPic?temp=**********", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:06.664",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-29",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.ValidateCodeController#get()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:06.664",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getLoginPic()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:06.676",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-29",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:06.676",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-29",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@7428166f]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:06.677",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-29",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:07.142",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:07.142",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@52ed191b]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:07.142",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:19.820",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "POST "/api/admin/login", parameters={}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:19.822",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#SystemAdminLogin(SystemAdminLoginRequest, HttpServletRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:19.827",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Read "application/json;charset=UTF-8" to [SystemAdminLoginRequest(account=admin, pwd=BzX36YjvNR8o, key=fd9863aa8ef9ff3022b5695ce306eaa3, code= (truncated)...]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:20.320",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:20.320",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@31b128d2]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:20.320",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:20.340",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-11",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/store/staff/list?page=1&limit=9999&temp=1754530580", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:20.340",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-28",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getAdminInfoByToken?token=9c01e5ed20784ce7a46a4210aae65538&temp=1754530580", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:20.340",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemStoreStaffController#getList(Integer, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:20.340",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-28",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getAdminInfo()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:20.340",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-28",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:20.340",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-28",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@3183c296]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:20.340",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-28",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:20.353",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-25",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getMenus?temp=1754530580", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:20.353",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-25",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getMenus()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:20.436",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:20.436",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@4845cd22]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:20.436",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-11",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:20.829",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-25",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:20.829",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-25",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@7bf9c54c]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:20.829",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-25",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:20.908",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-22",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_lefttop&temp=1754530580", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:20.908",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-17",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/info?formId=100&temp=1754530580", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:20.908",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-21",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_square&temp=1754530580", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:20.908",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:20.908",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-17",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#info(Integer)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:20.909",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-21",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:21.003",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:21.003",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@4b5128ed]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:21.013",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-22",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:21.138",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-17",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:21.138",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-17",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@6378c988]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:21.143",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-17",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:21.326",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-21",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:21.326",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-21",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@4e16fd66]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:21.328",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-21",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:24.400",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-6",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/brand/list?page=1&limit=20&name=&type=-1&temp=1754530584", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:24.402",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-15",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/group/data/list?gid=74&temp=1754530584", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:24.402",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-15",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemGroupDataController#getList(SystemGroupDataSearchRequest, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:24.402",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-6",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreBrandController#getBrandList(Integer, String, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:24.810",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-15",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:24.810",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-15",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@adf449d]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:24.819",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-15",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:29.436",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-6",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:29.436",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-6",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@582cb27f]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:36:29.436",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-6",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:37.554",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-3",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getAdminInfoByToken?token=9c01e5ed20784ce7a46a4210aae65538&temp=1754530717", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:37.555",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-3",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getAdminInfo()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:37.559",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-3",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:37.559",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-3",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@78d6d084]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:37.562",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-3",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:37.580",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-4",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getMenus?temp=1754530717", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:37.581",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-4",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getMenus()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:37.818",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-4",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:37.818",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-4",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@22c1a775]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:37.822",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-4",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:38.189",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-27",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/brand/list?page=1&limit=20&name=&type=-1&temp=1754530718", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:38.189",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-16",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/group/data/list?gid=74&temp=1754530718", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:38.189",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-28",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_square&temp=1754530718", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:38.190",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_lefttop&temp=1754530718", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:38.190",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-16",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemGroupDataController#getList(SystemGroupDataSearchRequest, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:38.190",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-27",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreBrandController#getBrandList(Integer, String, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:38.190",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-28",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:38.190",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:38.423",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-28",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:38.424",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-28",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@2eb14635]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:38.426",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-28",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:38.480",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-16",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:38.480",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-16",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@50d8a801]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:38.483",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-16",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:38.606",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:38.606",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@530b0410]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:38.608",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:40.041",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-27",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:40.042",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-27",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@6abc7ceb]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-07 09:38:40.045",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-27",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
